import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';
import { AuthService } from './services/auth.service';
import { filter, take } from 'rxjs/operators';
import { createLogger } from './shared/utils';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavHeaderComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  title = 'experienceStudio';
  userProfile: any;
  private logger = createLogger('AppComponent');

  constructor(public authService: AuthService) {}

  async ngOnInit() {
    try {
      // Wait for authentication state to be determined
      this.authService.userProfile$.subscribe(profile => {
        this.userProfile = profile;
      });
      await new Promise<void>(resolve => {
        this.authService.isAuthenticated$
          .pipe(
            filter(isAuthenticated => isAuthenticated !== undefined),
            take(1)
          )
          .subscribe(async isAuthenticated => {
            // Only trigger login if not authenticated and not already in a login flow
            if (!isAuthenticated && !this.authService.isLoginInProgressState()) {
              await this.authService.login();
            }
            resolve();
          });
      });
    } catch (error) {
      this.logger.error('Error during authentication:', error);
    }
  }
}
