import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';

/**
 * Typewriter animation modes
 */
export enum TypewriterMode {
  /** Type once and stop */
  SINGLE = 'single',
  /** Type, pause, erase, repeat with next text */
  CYCLING = 'cycling',
  /** Type multiple texts in sequence without erasing */
  SEQUENCE = 'sequence',
  /** Type text in batches (like logs) */
  BATCH = 'batch'
}

/**
 * Typewriter animation state
 */
export enum TypewriterState {
  IDLE = 'idle',
  TYPING = 'typing',
  PAUSED = 'paused',
  ERASING = 'erasing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

/**
 * Content type for different typing speeds
 */
export enum ContentType {
  TEXT = 'text',
  CODE = 'code',
  MARKDOWN = 'markdown',
  JSON = 'json',
  HTML = 'html'
}

/**
 * Configuration for typewriter animation
 */
export interface TypewriterConfig {
  /** Animation mode */
  mode: TypewriterMode;
  /** Typing speed in milliseconds per character */
  typingSpeed?: number;
  /** Erasing speed in milliseconds per character */
  erasingSpeed?: number;
  /** Pause before erasing (cycling mode only) */
  pauseBeforeErasing?: number;
  /** Pause before typing next text */
  pauseBeforeTyping?: number;
  /** Static text prefix */
  staticText?: string;
  /** Content type for adaptive speed */
  contentType?: ContentType;
  /** Whether to shuffle texts in cycling mode */
  shuffle?: boolean;
  /** Whether to show cursor */
  showCursor?: boolean;
  /** Custom cursor character */
  cursorChar?: string;
  /** Whether to preserve whitespace */
  preserveWhitespace?: boolean;
  /** Callback when animation completes */
  onComplete?: () => void;
  /** Callback when each text completes */
  onTextComplete?: (text: string, index: number) => void;
  /** Callback on state change */
  onStateChange?: (state: TypewriterState) => void;
}

/**
 * Typewriter animation instance
 */
export interface TypewriterInstance {
  id: string;
  config: TypewriterConfig;
  texts: string[];
  currentText: string;
  visibleText: string;
  currentTextIndex: number;
  currentCharIndex: number;
  state: TypewriterState;
  isTyping: boolean;
  animationTimeout?: any;
  pauseTimeout?: any;
  subject: BehaviorSubject<string>;
  stateSubject: BehaviorSubject<TypewriterState>;
}

/**
 * Default configuration values
 */
const DEFAULT_CONFIG: Partial<TypewriterConfig> = {
  mode: TypewriterMode.SINGLE,
  typingSpeed: 50,
  erasingSpeed: 30,
  pauseBeforeErasing: 1000,
  pauseBeforeTyping: 500,
  staticText: '',
  contentType: ContentType.TEXT,
  shuffle: false,
  showCursor: true,
  cursorChar: '|',
  preserveWhitespace: true
};

/**
 * Speed configurations for different content types
 */
const CONTENT_TYPE_SPEEDS: Record<ContentType, { typing: number; erasing: number }> = {
  [ContentType.TEXT]: { typing: 50, erasing: 30 },
  [ContentType.CODE]: { typing: 30, erasing: 20 },
  [ContentType.MARKDOWN]: { typing: 40, erasing: 25 },
  [ContentType.JSON]: { typing: 25, erasing: 15 },
  [ContentType.HTML]: { typing: 35, erasing: 20 }
};

@Injectable({
  providedIn: 'root'
})
export class TypewriterService {
  private instances = new Map<string, TypewriterInstance>();
  private instanceCounter = 0;

  // Legacy support for existing placeholder functionality
  private placeholderSubject = new BehaviorSubject<string>('');
  public placeholder$: Observable<string> = this.placeholderSubject.asObservable();

  constructor() {}

  /**
   * Create a new typewriter animation instance
   */
  createInstance(texts: string[], config: Partial<TypewriterConfig> = {}): string {
    const instanceId = `typewriter_${++this.instanceCounter}`;
    const finalConfig = { ...DEFAULT_CONFIG, ...config } as TypewriterConfig;

    // Apply content type specific speeds if not explicitly set
    if (finalConfig.contentType && !config.typingSpeed) {
      finalConfig.typingSpeed = CONTENT_TYPE_SPEEDS[finalConfig.contentType].typing;
    }
    if (finalConfig.contentType && !config.erasingSpeed) {
      finalConfig.erasingSpeed = CONTENT_TYPE_SPEEDS[finalConfig.contentType].erasing;
    }

    const processedTexts = finalConfig.shuffle ? this.shuffleArray([...texts]) : [...texts];

    const instance: TypewriterInstance = {
      id: instanceId,
      config: finalConfig,
      texts: processedTexts,
      currentText: '',
      visibleText: '',
      currentTextIndex: 0,
      currentCharIndex: 0,
      state: TypewriterState.IDLE,
      isTyping: true,
      subject: new BehaviorSubject<string>(''),
      stateSubject: new BehaviorSubject<TypewriterState>(TypewriterState.IDLE)
    };

    this.instances.set(instanceId, instance);
    return instanceId;
  }

  /**
   * Start animation for an instance
   */
  start(instanceId: string): void {
    const instance = this.instances.get(instanceId);
    if (!instance || instance.texts.length === 0) {
      return;
    }

    this.updateState(instance, TypewriterState.TYPING);
    this.startAnimation(instance);
  }

  /**
   * Pause animation for an instance
   */
  pause(instanceId: string): void {
    const instance = this.instances.get(instanceId);
    if (!instance) return;

    this.clearTimeouts(instance);
    this.updateState(instance, TypewriterState.PAUSED);
  }

  /**
   * Resume animation for an instance
   */
  resume(instanceId: string): void {
    const instance = this.instances.get(instanceId);
    if (!instance || instance.state !== TypewriterState.PAUSED) return;

    this.updateState(instance, TypewriterState.TYPING);
    this.startAnimation(instance);
  }

  /**
   * Stop and destroy an instance
   */
  stop(instanceId: string): void {
    const instance = this.instances.get(instanceId);
    if (!instance) return;

    this.clearTimeouts(instance);
    this.updateState(instance, TypewriterState.IDLE);
    instance.subject.complete();
    instance.stateSubject.complete();
    this.instances.delete(instanceId);
  }

  /**
   * Get observable for instance text output
   */
  getText$(instanceId: string): Observable<string> | null {
    const instance = this.instances.get(instanceId);
    return instance ? instance.subject.asObservable() : null;
  }

  /**
   * Get observable for instance state
   */
  getState$(instanceId: string): Observable<TypewriterState> | null {
    const instance = this.instances.get(instanceId);
    return instance ? instance.stateSubject.asObservable() : null;
  }

  /**
   * Get current visible text for an instance
   */
  getCurrentText(instanceId: string): string {
    const instance = this.instances.get(instanceId);
    return instance ? instance.visibleText : '';
  }

  /**
   * Get current state for an instance
   */
  getCurrentState(instanceId: string): TypewriterState {
    const instance = this.instances.get(instanceId);
    return instance ? instance.state : TypewriterState.IDLE;
  }

  /**
   * Add text to an existing instance (for batch mode)
   */
  addText(instanceId: string, text: string): void {
    const instance = this.instances.get(instanceId);
    if (!instance) return;

    instance.texts.push(text);

    // If in batch mode and not currently animating, start animation
    if (instance.config.mode === TypewriterMode.BATCH &&
        instance.state === TypewriterState.IDLE) {
      this.start(instanceId);
    }
  }

  /**
   * Update configuration for an instance
   */
  updateConfig(instanceId: string, config: Partial<TypewriterConfig>): void {
    const instance = this.instances.get(instanceId);
    if (!instance) return;

    instance.config = { ...instance.config, ...config };
  }

  /**
   * Start the core animation logic
   */
  private startAnimation(instance: TypewriterInstance): void {
    if (instance.currentTextIndex >= instance.texts.length) {
      this.handleAnimationComplete(instance);
      return;
    }

    instance.currentText = instance.texts[instance.currentTextIndex];
    this.animateText(instance);
  }

  /**
   * Animate the current text
   */
  private animateText(instance: TypewriterInstance): void {
    const { config, currentText, currentCharIndex, isTyping } = instance;

    if (isTyping) {
      // Typing phase
      if (currentCharIndex < currentText.length) {
        instance.currentCharIndex++;
        this.updateVisibleText(instance);

        const delay = this.calculateTypingDelay(instance);
        instance.animationTimeout = setTimeout(() => {
          this.animateText(instance);
        }, delay);
      } else {
        // Finished typing current text
        this.handleTextComplete(instance);
      }
    } else {
      // Erasing phase (only for cycling mode)
      if (currentCharIndex > 0) {
        instance.currentCharIndex--;
        this.updateVisibleText(instance);

        instance.animationTimeout = setTimeout(() => {
          this.animateText(instance);
        }, config.erasingSpeed);
      } else {
        // Finished erasing, move to next text
        this.moveToNextText(instance);
      }
    }
  }

  /**
   * Calculate typing delay based on content type and character
   */
  private calculateTypingDelay(instance: TypewriterInstance): number {
    const { config, currentText, currentCharIndex } = instance;
    const currentChar = currentText[currentCharIndex - 1];
    const baseSpeed = config.typingSpeed || 50;

    // Adaptive speed based on character type
    if (config.contentType === ContentType.CODE) {
      // Faster for common code patterns
      if (/[{}\[\]()<>]/.test(currentChar)) {
        return baseSpeed * 0.5; // Brackets type faster
      }
      if (/[a-zA-Z0-9_]/.test(currentChar)) {
        return baseSpeed * 0.7; // Identifiers type faster
      }
      if (/[\s\n\t]/.test(currentChar)) {
        return baseSpeed * 0.3; // Whitespace types very fast
      }
    }

    // Punctuation pauses
    if (/[.!?]/.test(currentChar)) {
      return baseSpeed * 2; // Pause after sentences
    }
    if (/[,;:]/.test(currentChar)) {
      return baseSpeed * 1.5; // Pause after clauses
    }

    return baseSpeed;
  }

  /**
   * Update the visible text and emit to subscribers
   */
  private updateVisibleText(instance: TypewriterInstance): void {
    const { config, currentText, currentCharIndex } = instance;

    let visibleText = currentText.substring(0, currentCharIndex);

    // Add static text prefix if configured
    if (config.staticText) {
      visibleText = `${config.staticText} ${visibleText}`;
    }

    // Add cursor if enabled and currently typing
    if (config.showCursor && instance.state === TypewriterState.TYPING) {
      visibleText += config.cursorChar;
    }

    instance.visibleText = visibleText;
    instance.subject.next(visibleText);
  }

  /**
   * Handle completion of current text
   */
  private handleTextComplete(instance: TypewriterInstance): void {
    const { config } = instance;

    // Call text complete callback
    if (config.onTextComplete) {
      config.onTextComplete(instance.currentText, instance.currentTextIndex);
    }

    // Determine next action based on mode
    switch (config.mode) {
      case TypewriterMode.SINGLE:
        this.handleAnimationComplete(instance);
        break;

      case TypewriterMode.CYCLING:
        // Start erasing after pause
        instance.pauseTimeout = setTimeout(() => {
          instance.isTyping = false;
          this.updateState(instance, TypewriterState.ERASING);
          this.animateText(instance);
        }, config.pauseBeforeErasing);
        break;

      case TypewriterMode.SEQUENCE:
      case TypewriterMode.BATCH:
        // Move to next text immediately
        this.moveToNextText(instance);
        break;
    }
  }

  /**
   * Move to the next text in the sequence
   */
  private moveToNextText(instance: TypewriterInstance): void {
    instance.currentTextIndex++;
    instance.currentCharIndex = 0;
    instance.isTyping = true;

    if (instance.currentTextIndex >= instance.texts.length) {
      if (instance.config.mode === TypewriterMode.CYCLING) {
        // Restart from beginning in cycling mode
        instance.currentTextIndex = 0;
      } else {
        // Complete animation for other modes
        this.handleAnimationComplete(instance);
        return;
      }
    }

    // Pause before typing next text
    instance.pauseTimeout = setTimeout(() => {
      this.updateState(instance, TypewriterState.TYPING);
      this.startAnimation(instance);
    }, instance.config.pauseBeforeTyping);
  }

  /**
   * Handle animation completion
   */
  private handleAnimationComplete(instance: TypewriterInstance): void {
    this.updateState(instance, TypewriterState.COMPLETED);

    // Remove cursor from final text
    if (instance.config.showCursor) {
      let finalText = instance.visibleText;
      if (finalText.endsWith(instance.config.cursorChar || '|')) {
        finalText = finalText.slice(0, -1);
        instance.visibleText = finalText;
        instance.subject.next(finalText);
      }
    }

    // Call completion callback
    if (instance.config.onComplete) {
      instance.config.onComplete();
    }
  }

  /**
   * Update instance state and notify subscribers
   */
  private updateState(instance: TypewriterInstance, state: TypewriterState): void {
    instance.state = state;
    instance.stateSubject.next(state);

    if (instance.config.onStateChange) {
      instance.config.onStateChange(state);
    }
  }

  /**
   * Clear all timeouts for an instance
   */
  private clearTimeouts(instance: TypewriterInstance): void {
    if (instance.animationTimeout) {
      clearTimeout(instance.animationTimeout);
      instance.animationTimeout = undefined;
    }
    if (instance.pauseTimeout) {
      clearTimeout(instance.pauseTimeout);
      instance.pauseTimeout = undefined;
    }
  }

  /**
   * Fisher-Yates shuffle algorithm
   */
  private shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }

  // ===== LEGACY SUPPORT METHODS =====
  // These methods maintain backward compatibility with existing code

  /**
   * Legacy method: Start typewriter animation (cycling mode)
   * @deprecated Use createInstance() and start() instead
   */
  startTypewriter(
    texts: string[],
    staticText: string = '',
    typingSpeed: number = 40,
    erasingSpeed: number = 30,
    pauseBeforeErasing: number = 400,
    pauseBeforeTyping: number = 200
  ): void {
    // Stop any existing legacy animation
    this.stopTypewriter();

    // Create a new instance for legacy support
    const instanceId = this.createInstance(texts, {
      mode: TypewriterMode.CYCLING,
      staticText,
      typingSpeed,
      erasingSpeed,
      pauseBeforeErasing,
      pauseBeforeTyping,
      shuffle: true,
      showCursor: false
    });

    // Subscribe to updates and forward to legacy subject
    const instance = this.instances.get(instanceId);
    if (instance) {
      instance.subject.subscribe(text => {
        this.placeholderSubject.next(text);
      });

      // Store instance ID for legacy cleanup
      (this as any)._legacyInstanceId = instanceId;
    }

    this.start(instanceId);
  }

  /**
   * Legacy method: Stop typewriter animation
   * @deprecated Use stop() with instance ID instead
   */
  stopTypewriter(): void {
    const legacyInstanceId = (this as any)._legacyInstanceId;
    if (legacyInstanceId) {
      this.stop(legacyInstanceId);
      delete (this as any)._legacyInstanceId;
    }
  }

  // ===== CONVENIENCE METHODS =====

  /**
   * Create and start a simple single-text animation
   */
  typeText(text: string, config: Partial<TypewriterConfig> = {}): string {
    const instanceId = this.createInstance([text], {
      ...config,
      mode: TypewriterMode.SINGLE
    });
    this.start(instanceId);
    return instanceId;
  }

  /**
   * Create and start a cycling animation
   */
  cycleTexts(texts: string[], config: Partial<TypewriterConfig> = {}): string {
    const instanceId = this.createInstance(texts, {
      ...config,
      mode: TypewriterMode.CYCLING
    });
    this.start(instanceId);
    return instanceId;
  }

  /**
   * Create and start a sequence animation
   */
  sequenceTexts(texts: string[], config: Partial<TypewriterConfig> = {}): string {
    const instanceId = this.createInstance(texts, {
      ...config,
      mode: TypewriterMode.SEQUENCE
    });
    this.start(instanceId);
    return instanceId;
  }

  /**
   * Create a batch animation instance (for logs, etc.)
   */
  createBatch(config: Partial<TypewriterConfig> = {}): string {
    return this.createInstance([], {
      ...config,
      mode: TypewriterMode.BATCH
    });
  }

  /**
   * Clean up all instances
   */
  cleanup(): void {
    for (const instanceId of this.instances.keys()) {
      this.stop(instanceId);
    }
  }
}
