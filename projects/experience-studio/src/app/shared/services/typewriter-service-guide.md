# Enhanced Typewriter Service

A comprehensive, feature-rich typewriter animation service for Angular applications with multiple animation modes, adaptive typing speeds, and reactive state management.

## Features

- ✅ **Multiple Animation Modes**: Single, Cycling, Sequence, and Batch
- ✅ **Adaptive Typing Speeds**: Different speeds for text, code, markdown, JSON, and HTML
- ✅ **Reactive State Management**: Observable-based with real-time state updates
- ✅ **Multiple Concurrent Animations**: Run multiple typewriter instances simultaneously
- ✅ **Memory Efficient**: Proper cleanup and resource management
- ✅ **Backward Compatible**: Legacy support for existing implementations
- ✅ **Accessible**: Reduced motion support and customizable cursors
- ✅ **Pause/Resume**: Full control over animation lifecycle
- ✅ **Content-Aware**: Smart character-by-character timing based on content type

## Quick Start

### Basic Usage

```typescript
import { TypewriterService } from './shared/services/typewriter.service';

constructor(private typewriterService: TypewriterService) {}

// Simple single text animation
const instanceId = this.typewriterService.typeText('Hello World!', {
  typingSpeed: 50,
  showCursor: true
});

// Get text updates
this.typewriterService.getText$(instanceId)?.subscribe(text => {
  console.log('Current text:', text);
});
```

### Using the Directive

```html
<!-- Simple text animation -->
<div appTypewriter="Welcome to our application!"></div>

<!-- Multiple texts with cycling -->
<div appTypewriter 
     [texts]="['Create apps', 'Build interfaces', 'Design experiences']"
     [mode]="'cycling'"
     [typingSpeed]="40"
     [showCursor]="true">
</div>

<!-- Placeholder animation -->
<input appTypewriter
       [texts]="['Enter your name...', 'Type something...']"
       [mode]="'cycling'"
       [targetAttribute]="'placeholder'">
```

### Using the Component

```html
<app-typewriter 
  [texts]="['Step 1: Initialize', 'Step 2: Configure', 'Step 3: Deploy']"
  [mode]="'sequence'"
  [contentType]="'code'"
  [showCursor]="true"
  [theme]="'dark'"
  (onComplete)="handleComplete()"
  (onStateChange)="handleStateChange($event)">
</app-typewriter>
```

## Animation Modes

### 1. Single Mode
Types text once and stops.

```typescript
const instanceId = this.typewriterService.createInstance(['Hello World!'], {
  mode: TypewriterMode.SINGLE,
  typingSpeed: 50
});
this.typewriterService.start(instanceId);
```

### 2. Cycling Mode
Types text, erases it, then moves to the next text in a loop.

```typescript
const instanceId = this.typewriterService.cycleTexts([
  'Create amazing apps',
  'Build beautiful interfaces',
  'Design stunning experiences'
], {
  typingSpeed: 60,
  erasingSpeed: 40,
  pauseBeforeErasing: 2000,
  pauseBeforeTyping: 500
});
```

### 3. Sequence Mode
Types multiple texts in sequence without erasing.

```typescript
const instanceId = this.typewriterService.sequenceTexts([
  'Step 1: Initialize project',
  'Step 2: Configure settings',
  'Step 3: Build application'
], {
  typingSpeed: 40,
  pauseBeforeTyping: 800
});
```

### 4. Batch Mode
Perfect for logs - add text dynamically and animate in sequence.

```typescript
const batchId = this.typewriterService.createBatch({
  contentType: ContentType.TEXT,
  typingSpeed: 20
});

// Add logs dynamically
this.typewriterService.addText(batchId, '[INFO] Application started');
this.typewriterService.addText(batchId, '[DEBUG] Loading configuration');
this.typewriterService.addText(batchId, '[SUCCESS] Ready to serve requests');
```

## Content Types

The service automatically adjusts typing speeds based on content type:

- **TEXT**: 50ms per character (default)
- **CODE**: 30ms per character (faster for code)
- **MARKDOWN**: 40ms per character
- **JSON**: 25ms per character (fastest)
- **HTML**: 35ms per character

```typescript
const instanceId = this.typewriterService.typeText(codeSnippet, {
  contentType: ContentType.CODE,
  showCursor: true
});
```

## Advanced Features

### State Management

```typescript
// Subscribe to state changes
this.typewriterService.getState$(instanceId)?.subscribe(state => {
  switch(state) {
    case TypewriterState.TYPING:
      console.log('Currently typing...');
      break;
    case TypewriterState.PAUSED:
      console.log('Animation paused');
      break;
    case TypewriterState.COMPLETED:
      console.log('Animation completed');
      break;
  }
});
```

### Control Methods

```typescript
// Pause animation
this.typewriterService.pause(instanceId);

// Resume animation
this.typewriterService.resume(instanceId);

// Stop and cleanup
this.typewriterService.stop(instanceId);

// Get current text
const currentText = this.typewriterService.getCurrentText(instanceId);

// Get current state
const currentState = this.typewriterService.getCurrentState(instanceId);
```

### Callbacks

```typescript
const instanceId = this.typewriterService.createInstance(texts, {
  onComplete: () => console.log('Animation finished!'),
  onTextComplete: (text, index) => console.log(`Completed: ${text}`),
  onStateChange: (state) => console.log(`State changed to: ${state}`)
});
```

## Integration Examples

### Vertical Stepper Integration

The vertical stepper component has been updated to use the enhanced typewriter service:

```typescript
// In vertical-stepper.component.ts
private startTypewriterAnimation(stepIndex: number): void {
  const step = this.steps[stepIndex];
  
  const instanceId = this.typewriterService.createInstance([step.description], {
    mode: TypewriterMode.SINGLE,
    typingSpeed: this.typingSpeed,
    contentType: ContentType.MARKDOWN,
    showCursor: false,
    onComplete: () => {
      step.isTyping = false;
      this.cdr.detectChanges();
    }
  });

  this.stepTypewriterInstances[stepIndex] = instanceId;
  this.typewriterService.start(instanceId);
}
```

### Logs Implementation

For implementing in logs (like code-window component):

```typescript
// Create batch instance for logs
private logTypewriterInstance = this.typewriterService.createBatch({
  contentType: ContentType.TEXT,
  typingSpeed: 80, // Slower for dramatic effect
  pauseBeforeTyping: 200
});

// Add new log entries
addLogEntry(logMessage: string): void {
  this.typewriterService.addText(this.logTypewriterInstance, logMessage);
}

// Subscribe to updates
ngOnInit(): void {
  this.typewriterService.getText$(this.logTypewriterInstance)?.subscribe(text => {
    this.displayedLogs = text;
    this.cdr.detectChanges();
  });
}
```

## Best Practices

1. **Always clean up instances** in `ngOnDestroy`
2. **Use appropriate content types** for optimal speeds
3. **Leverage batch mode** for dynamic content like logs
4. **Use observables** for reactive UI updates
5. **Consider accessibility** with reduced motion preferences

## Migration from Legacy

The service maintains backward compatibility:

```typescript
// Legacy method still works
this.typewriterService.startTypewriter(texts, staticText, typingSpeed);

// But prefer the new API
const instanceId = this.typewriterService.cycleTexts(texts, {
  staticText,
  typingSpeed
});
```

## Performance

- **Memory efficient**: Automatic cleanup with Angular's DestroyRef
- **Optimized animations**: Uses requestAnimationFrame for smooth performance
- **Concurrent support**: Multiple instances without interference
- **Smart timing**: Adaptive speeds based on content patterns
