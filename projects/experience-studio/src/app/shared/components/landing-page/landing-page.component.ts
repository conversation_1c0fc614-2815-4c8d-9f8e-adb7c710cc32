import { Component, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HeroSectionHeaderComponent } from '../hero-section-header/hero-section-header.component';
import { CardDataService } from '../../services/data-services/card-data.service';
import { RecentCreationComponent } from '../recent-creation/recent-creation.component';
import { ThemeService } from '../../services/theme-service/theme.service';
import { ToastService } from '../../services/toast.service';
import { CardSelectionService } from '../../services/card-selection.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';

// PREVIOUS CARD DESIGN: These imports were used for the previous card design
// import { ButtonComponent, CardsComponent } from '@awe/play-comp-library';

// PREVIOUS CARD DESIGN: Original card interface for backward compatibility
// Keeping this commented for reference in case we need to revert back
/*
interface CardData {
  id: number;
  isFlipped: boolean;
  frontTitle: string;
  frontDescription: string;
  frontImage: string;
  backDescription: string;
  buttonText: string;
  redirectPath: string;
}
*/

// New studio card interface based on elder-wand studios component
interface StudioCard {
  id: number;
  title: string;
  description: string;
  image: string;
  path: string;
  type: string;
  disabled?: boolean; // Flag to indicate if the card is disabled
}

@Component({
  selector: 'app-landing-page',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeroSectionHeaderComponent,
    RecentCreationComponent,
  ],
  templateUrl: './landing-page.component.html',
  styleUrl: './landing-page.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LandingPageComponent implements OnInit {
  cardImage: string = 'assets/cards-images/card-img-black.png';
  card_1: string = 'assets/cards-images/card_1.png';
  card_2: string = 'assets/cards-images/card_2.png';
  theme: 'light' | 'dark' = 'light';

  private subscriptionManager = new SubscriptionManager();

  // PREVIOUS CARD DESIGN: Original card data array
  // Keeping this commented for reference in case we need to revert back
  /*
  cardData: CardData[] = [
    {
      id: 1,
      isFlipped: false,
      frontTitle: 'Generate UI Design',
      frontDescription: 'Create and generate UI designs effortlessly with AI',
      frontImage: this.card_1,
      backDescription:
        'The AI-driven agent transforms user prompts into fully applications. Using advanced algorithms, it interprets requirements, generates visually appealing and user-friendly interfaces, and streamlines the design process.',
      buttonText: 'Generate',
      redirectPath: 'prompt',
    },
    {
      id: 2,
      isFlipped: false,
      frontTitle: 'Generate Application',
      frontDescription: 'Create complete applications with AI assistance',
      frontImage: this.card_2,
      backDescription:
        'Generate fully functional applications based on your requirements. Our AI will analyze your needs and create a complete application with proper structure, functionality, and design elements.',
      buttonText: 'Generate',
      redirectPath: 'prompt',
    },
  ];
  */

  // New studio cards data based on elder-wand studios component
  studioCards: StudioCard[] = [
    {
      id: 1,
      title: 'Generate UI Design',
      description: 'Create and generate UI designs with AI. Transform your ideas into beautiful interfaces.',
      image: '/assets/cards-images/ui_design.svg',
      path: 'prompt',
      type: 'prompt-to-code',
      disabled: true // Disable the Generate UI Design card
    },
    {
      id: 2,
      title: 'Generate Application',
      description: 'Create complete applications with AI. From concept to functional code in minutes.',
      image: '/assets/cards-images/app_generation.svg',
      path: 'prompt',
      type: 'image-to-code'
    }
  ];

  // Get card background color based on theme
  getCardBackground(): string {
    return this.theme === 'dark' ? 'transparent' : 'transparent';
  }

  // Get card text color based on theme
  getCardTextColor(): string {
    return this.theme === 'dark' ? '#ffffff' : '#1D1D1D';
  }

  // Get card description color based on theme
  getCardDescriptionColor(): string {
    return this.theme === 'dark' ? '#cccccc' : '#595959';
  }

  // TrackBy function for ngFor performance optimization
  trackByCardId(index: number, card: StudioCard): number {
    return card.id;
  }

  constructor(
    private router: Router,
    private cardDataService: CardDataService,
    private themeService: ThemeService,
    private toastService: ToastService,
    private cardSelectionService: CardSelectionService
  ) {}

  ngOnInit(): void {
    // Initialize theme
    this.initTheme();

    // Reset card selection state when landing page is loaded
    this.cardSelectionService.resetSelectionState();
  }

  private initTheme(): void {
    // Get current theme
    this.theme = this.themeService.getCurrentTheme();

    // Subscribe to theme changes
    this.subscriptionManager.subscribe(
      this.themeService.themeObservable,
      (theme: 'light' | 'dark') => {
        this.theme = theme;
      }
    );
  }

  // PREVIOUS CARD DESIGN: Method to flip the card
  // Keeping this commented for reference in case we need to revert back
  /*
  flipCard(card: CardData, isHovering: boolean): void {
    card.isFlipped = isHovering;
  }
  */

  // PREVIOUS CARD DESIGN: Method to navigate to a specific route (for original cards)
  // Keeping this commented for reference in case we need to revert back
  /*
  navigateTo(path: string, event: Event, cardTitle: string): void {
    event.stopPropagation();
    this.cardDataService.setSelectedCardTitle(cardTitle);

    // Determine the base route based on the card title
    let baseRoute = '';
    if (cardTitle === 'Generate UI Design') {
      baseRoute = '/experience/prompt-to-code';
    } else if (cardTitle === 'Generate Application' || cardTitle === 'Code Generation') {
      baseRoute = '/experience/image-to-code';
    }

    // Navigate to the appropriate route
    if (path === 'prompt') {
      this.router.navigate([`${baseRoute}/prompt`]);
    } else if (path === 'form') {
      this.router.navigate([`${baseRoute}/form`]);
    } else if (path === 'analytics') {
      this.router.navigate([`${baseRoute}/code-preview`]);
    }
  }
  */

  // Method to navigate from studio cards
  navigateToStudio(card: StudioCard, event: Event): void {
    event.stopPropagation();

    // Check if the card is disabled
    if (card.disabled) {
      // Show a toast notification that this feature is disabled
      this.toastService.info('This feature is in build mode.');
      return; // Exit the method early
    }

    this.cardDataService.setSelectedCardTitle(card.title);

    // Mark that a card has been selected - this will allow the guard to pass
    this.cardSelectionService.setCardSelected(true);

    // Determine the route based on the card title
    let routePrefix = '';
    if (card.title === 'Generate UI Design') {
      routePrefix = '/experience/generate-ui-design';
      this.toastService.info('Starting UI Design generation');
    } else if (card.title === 'Generate Application') {
      routePrefix = '/experience/generate-application';
      this.toastService.info('Starting Application generation');
    }

    // Navigate to the appropriate prompt route
    this.router.navigate([`${routePrefix}/prompt`]);
  }
}
