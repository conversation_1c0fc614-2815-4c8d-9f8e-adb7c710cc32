.typewriter-container {
  display: inline-block;
  position: relative;
  font-family: inherit;
  line-height: inherit;

  &.light {
    color: var(--text-primary, #333);
  }

  &.dark {
    color: var(--text-primary-dark, #fff);
  }

  &.typing {
    .typewriter-cursor {
      animation: none;
      opacity: 1;
    }
  }

  &.paused {
    .typewriter-cursor {
      animation: blink 1s infinite;
    }
  }

  &.completed {
    .typewriter-cursor {
      display: none;
    }
  }
}

.typewriter-text {
  display: inline;
  white-space: pre-wrap;
  word-wrap: break-word;
  
  // Preserve formatting for code content
  &.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    white-space: pre;
  }
}

.typewriter-cursor {
  display: inline-block;
  font-weight: normal;
  color: currentColor;
  animation: blink 1s infinite;
  margin-left: 1px;

  &.blinking {
    animation: blink 1s infinite;
  }

  // Different cursor styles
  &.block {
    background-color: currentColor;
    color: transparent;
    width: 0.6em;
    height: 1em;
    display: inline-block;
    animation: blink-block 1s infinite;
  }

  &.underline {
    border-bottom: 2px solid currentColor;
    animation: blink 1s infinite;
  }
}

// Cursor animations
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes blink-block {
  0%, 50% {
    background-color: currentColor;
  }
  51%, 100% {
    background-color: transparent;
  }
}

// Theme-specific styles
.typewriter-container.light {
  .typewriter-text {
    color: var(--text-primary-light, #333);
  }
  
  .typewriter-cursor {
    color: var(--accent-color-light, #007bff);
  }
}

.typewriter-container.dark {
  .typewriter-text {
    color: var(--text-primary-dark, #fff);
  }
  
  .typewriter-cursor {
    color: var(--accent-color-dark, #4dabf7);
  }
}

// Content type specific styles
.typewriter-container.code {
  .typewriter-text {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    background-color: var(--code-bg, #f8f9fa);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    border: 1px solid var(--code-border, #e9ecef);
  }

  &.dark .typewriter-text {
    background-color: var(--code-bg-dark, #2d3748);
    border-color: var(--code-border-dark, #4a5568);
  }
}

.typewriter-container.markdown {
  .typewriter-text {
    line-height: 1.6;
  }
}

// Size variants
.typewriter-container.small {
  font-size: 0.875em;
}

.typewriter-container.large {
  font-size: 1.25em;
}

.typewriter-container.xl {
  font-size: 1.5em;
}

// Animation speed classes
.typewriter-container.fast {
  .typewriter-cursor {
    animation-duration: 0.5s;
  }
}

.typewriter-container.slow {
  .typewriter-cursor {
    animation-duration: 2s;
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .typewriter-cursor {
    animation: none;
    opacity: 1;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .typewriter-container {
    .typewriter-text {
      color: CanvasText;
    }
    
    .typewriter-cursor {
      color: CanvasText;
      border-color: CanvasText;
    }
  }
}

// Focus styles for interactive elements
.typewriter-container:focus-within {
  outline: 2px solid var(--focus-color, #007bff);
  outline-offset: 2px;
  border-radius: 2px;
}
