import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  TypewriterService,
  TypewriterMode,
  TypewriterState,
  ContentType,
  TypewriterConfig
} from '../../services/typewriter.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';

/**
 * Standalone typewriter component for complex scenarios
 *
 * Usage examples:
 *
 * Basic usage:
 * <app-typewriter [texts]="['Hello', 'World']"></app-typewriter>
 *
 * Advanced usage:
 * <app-typewriter
 *   [texts]="myTexts"
 *   [mode]="'cycling'"
 *   [contentType]="'code'"
 *   [showCursor]="true"
 *   [cursorChar]="'_'"
 *   [typingSpeed]="30"
 *   [theme]="'dark'"
 *   (onComplete)="handleComplete()"
 *   (onStateChange)="handleStateChange($event)">
 * </app-typewriter>
 */
@Component({
  selector: 'app-typewriter',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div
      class="typewriter-container"
      [class]="theme"
      [class.typing]="currentState === 'typing'"
      [class.paused]="currentState === 'paused'"
      [class.completed]="currentState === 'completed'">

      <span
        class="typewriter-text"
        [innerHTML]="displayText">
      </span>

      <span
        *ngIf="showCursor && shouldShowCursor"
        class="typewriter-cursor"
        [class.blinking]="currentState !== 'typing'">
        {{ cursorChar }}
      </span>
    </div>
  `,
  styleUrls: ['./typewriter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TypewriterComponent implements OnInit, OnDestroy {
  @Input() texts: string[] = [];
  @Input() mode: TypewriterMode = TypewriterMode.SINGLE;
  @Input() typingSpeed?: number;
  @Input() erasingSpeed?: number;
  @Input() pauseBeforeErasing?: number;
  @Input() pauseBeforeTyping?: number;
  @Input() staticText?: string;
  @Input() contentType?: ContentType;
  @Input() shuffle?: boolean;
  @Input() showCursor: boolean = true;
  @Input() cursorChar: string = '|';
  @Input() preserveWhitespace?: boolean;
  @Input() autoStart: boolean = true;
  @Input() theme: string = 'light';

  @Output() onComplete = new EventEmitter<void>();
  @Output() onTextComplete = new EventEmitter<{ text: string; index: number }>();
  @Output() onStateChange = new EventEmitter<TypewriterState>();

  displayText: string = '';
  currentState: TypewriterState = TypewriterState.IDLE;
  shouldShowCursor: boolean = true;

  private instanceId?: string;
  private subscriptionManager = new SubscriptionManager();

  constructor(
    private typewriterService: TypewriterService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    if (this.autoStart && this.texts.length > 0) {
      this.startAnimation();
    }
  }

  ngOnDestroy(): void {
    this.stopAnimation();
    // Cleanup is handled automatically by DestroyRef in SubscriptionManager
  }

  /**
   * Start the typewriter animation
   */
  startAnimation(): void {
    this.stopAnimation(); // Clean up any existing animation

    if (this.texts.length === 0) {
      return;
    }

    const config: Partial<TypewriterConfig> = {
      mode: this.mode,
      typingSpeed: this.typingSpeed,
      erasingSpeed: this.erasingSpeed,
      pauseBeforeErasing: this.pauseBeforeErasing,
      pauseBeforeTyping: this.pauseBeforeTyping,
      staticText: this.staticText,
      contentType: this.contentType,
      shuffle: this.shuffle,
      showCursor: false, // We handle cursor display in template
      preserveWhitespace: this.preserveWhitespace,
      onComplete: () => {
        this.shouldShowCursor = false;
        this.onComplete.emit();
        this.cdr.detectChanges();
      },
      onTextComplete: (text: string, index: number) =>
        this.onTextComplete.emit({ text, index }),
      onStateChange: (state: TypewriterState) => {
        this.currentState = state;
        this.shouldShowCursor = state === TypewriterState.TYPING ||
                               state === TypewriterState.PAUSED;
        this.onStateChange.emit(state);
        this.cdr.detectChanges();
      }
    };

    this.instanceId = this.typewriterService.createInstance(this.texts, config);

    // Subscribe to text updates
    const textObservable = this.typewriterService.getText$(this.instanceId);
    if (textObservable) {
      this.subscriptionManager.subscribe(
        textObservable,
        (text: string) => {
          this.displayText = text;
          this.cdr.detectChanges();
        }
      );
    }

    // Subscribe to state updates
    const stateObservable = this.typewriterService.getState$(this.instanceId);
    if (stateObservable) {
      this.subscriptionManager.subscribe(
        stateObservable,
        (state: TypewriterState) => {
          this.currentState = state;
          this.shouldShowCursor = state === TypewriterState.TYPING ||
                                 state === TypewriterState.PAUSED;
          this.cdr.detectChanges();
        }
      );
    }

    this.typewriterService.start(this.instanceId);
  }

  /**
   * Stop the typewriter animation
   */
  stopAnimation(): void {
    if (this.instanceId) {
      this.typewriterService.stop(this.instanceId);
      this.instanceId = undefined;
    }
    this.displayText = '';
    this.currentState = TypewriterState.IDLE;
    this.shouldShowCursor = false;
    this.cdr.detectChanges();
  }

  /**
   * Pause the animation
   */
  pauseAnimation(): void {
    if (this.instanceId) {
      this.typewriterService.pause(this.instanceId);
    }
  }

  /**
   * Resume the animation
   */
  resumeAnimation(): void {
    if (this.instanceId) {
      this.typewriterService.resume(this.instanceId);
    }
  }

  /**
   * Add text to the animation (for batch mode)
   */
  addText(text: string): void {
    if (this.instanceId) {
      this.typewriterService.addText(this.instanceId, text);
    }
  }

  /**
   * Get the current state
   */
  getCurrentState(): TypewriterState {
    return this.currentState;
  }

  /**
   * Get the current visible text
   */
  getCurrentText(): string {
    return this.displayText;
  }

  /**
   * Update texts and restart animation
   */
  updateTexts(newTexts: string[]): void {
    this.texts = newTexts;
    if (this.autoStart) {
      this.startAnimation();
    }
  }
}
