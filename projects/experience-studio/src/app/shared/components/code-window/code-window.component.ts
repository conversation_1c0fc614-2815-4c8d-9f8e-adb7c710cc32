import {
  Component,
  HostListener,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  ChangeDetectorRef,
  // Renderer2 is imported but currently unused
  // Renderer2,
  Input,
  NgZone,
  ViewChild,
  AfterViewInit,
  ChangeDetectionStrategy,
} from '@angular/core';
import { Subscription, Subject, BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { takeUntil, map, distinctUntilChanged, shareReplay } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { createLogger } from '../../utils/logger';
import {
  IconsComponent,
  LeftPanelComponent,
  RightPanelComponent,
  SplitScreenComponent,
} from '@awe/play-comp-library';
import { TypewriterService, TypewriterMode, ContentType, TypewriterState } from '../../services/typewriter.service';
import { FileModel } from '../code-viewer/code-viewer.component';
import { SafeResourceUrl, DomSanitizer } from '@angular/platform-browser';
import { CodeGenerationService } from '../../services/code-generation.service';
import { CodeSharingService } from '../../services/code-sharing.service';
import { PollingService } from '../../services/polling.service';
import { ThemeService } from '../../services/theme-service/theme.service';
import { AppStateService } from '../../services/app-state.service';
import { PromptBarService } from '../../services/prompt-bar-servises/prompt-bar.service';
import { PromptSubmissionService } from '../../services/prompt-submission.service';
import { UserSignatureService } from '../../services/user-signature.service';
import { TextTransformationService } from '../../services/text-transformation.service';
import { StepperStateService } from '../../services/stepper-state.service';
import { ToastService } from '../../services/toast.service';
import { CodeViewerComponent } from '../code-viewer/code-viewer.component';
import { LoadingAnimationComponent } from './loading-animation/loading-animation.component';
import { MarkdownModule } from 'ngx-markdown';
import { ChatWindowComponent } from '../chat-window/chat-window.component';
import { ErrorPageComponent } from '../error-page/error-page.component';
import { StepperState } from '../../models/stepper-states.enum';
// import * as crypto from 'crypto-js';
import { Md5 } from 'ts-md5';

import JSZip from 'jszip';
import {
  ALL_DESIGN_TOKENS,
  DESIGN_TOKENS_STATS,
  isValidHexColor as validateHexColor
} from '../../data/design-tokens.data';

type IconStatus = 'default' | 'active' | 'disable';

/**
 * Interface for design tokens that can be edited
 */
interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
}

/**
 * Interface for font style tokens
 */
interface FontStyleToken {
  name: string;
  size: string;
  weight: string;
  lineHeight: string;
}

/**
 * Interface for color tokens
 */
interface ColorToken {
  name: string;
  value: string;
  hexCode: string;
}

/**
 * Interface for button tokens
 */
interface ButtonToken {
  label: string;
  variant: string;
  hasIcon?: boolean;
}

/**
 * Generates an app name for Azure websites based on folder path
 * @param folderPath The folder path to use for generating the app name
 * @returns A string with the generated app name
 */
function generateAppName(folderPath: string): string {
  // Convert folder path to base name with dashes instead of slashes
  let baseName = folderPath.replace(/\//g, '-').replace(/[^a-zA-Z0-9-]/g, '');

  // Trim leading/trailing dashes
  baseName = baseName.replace(/^-+|-+$/g, '');

  // Generate a short hash (first 6 characters of MD5)
  const hash = Md5.hashStr(folderPath);
  const shortHash = hash.substring(0, 6);
  const suffix = `-${shortHash}`;

  // Set max total length
  const maxLength = 60;
  // Reserve length for suffix (- + 6 char hash = 7 chars)
  const allowedBaseLength = maxLength - suffix.length;

  // Truncate baseName if necessary and remove trailing hyphens
  if (baseName.length > allowedBaseLength) {
    baseName = baseName.substring(0, allowedBaseLength).replace(/-+$/g, '');
  }

  // Construct final app name
  let appName = `${baseName}${suffix}`;

  // Ensure name is at least 3 characters
  while (appName.length < 3) {
    appName += '0';
  }
  return appName;
}
@Component({
  selector: 'app-code-window',
  standalone: true,
  imports: [
    CommonModule,
    SplitScreenComponent,
    LeftPanelComponent,
    RightPanelComponent,
    IconsComponent,
    CodeViewerComponent,
    LoadingAnimationComponent,
    ChatWindowComponent,
    ErrorPageComponent,
    MarkdownModule,
  ],
  templateUrl: './code-window.component.html',
  styleUrls: ['./code-window.component.scss', './artifacts-view.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CodeWindowComponent implements OnInit, AfterViewInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private logger = createLogger('CodeWindow');

  // Convert properties to observables for OnPush optimization
  private files$ = new BehaviorSubject<FileModel[]>([]);
  private isResizing$ = new BehaviorSubject<boolean>(false);
  private currentView$ = new BehaviorSubject<'loading' | 'editor' | 'preview' | 'logs' | 'artifacts'>('preview');
  private isPreviewLoading$ = new BehaviorSubject<boolean>(false);
  private previewIcon$ = new BehaviorSubject<string>('bi-code-slash');
  private previewError$ = new BehaviorSubject<boolean>(false);
  private errorDescription$ = new BehaviorSubject<string>('Please try again later.');
  private errorTerminalOutput$ = new BehaviorSubject<string>('');
  private currentTheme$ = new BehaviorSubject<'light' | 'dark'>('light');
  private deployedUrl$ = new BehaviorSubject<string | null>('https://orange-island-09111c10f.6.azurestaticapps.net/');
  private isPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private isLoading$ = new BehaviorSubject<boolean>(true);
  private isHistoryActive$ = new BehaviorSubject<boolean>(false);
  private isCodeActive$ = new BehaviorSubject<boolean>(false);
  private isPreviewActive$ = new BehaviorSubject<boolean>(true);
  private isLogsActive$ = new BehaviorSubject<boolean>(false);
  private isArtifactsActive$ = new BehaviorSubject<boolean>(false);
  private isLeftPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private minWidth$ = new BehaviorSubject<string>('300px');
  private isExperienceStudioModalOpen$ = new BehaviorSubject<boolean>(false);
  private checkSessionStorageInterval: any;
  private shouldHideProjectName$ = new BehaviorSubject<boolean>(false);

  // Public observable for template consumption
  readonly shouldHideProjectName = this.shouldHideProjectName$.asObservable();

  // Flag to track if user has manually selected a tab
  private userSelectedTab: boolean = false;

  // Public observables for template consumption
  readonly files = this.files$.asObservable();
  readonly isResizing = this.isResizing$.asObservable();
  readonly currentView = this.currentView$.asObservable();
  readonly isPreviewLoading = this.isPreviewLoading$.asObservable();
  readonly previewIcon = this.previewIcon$.asObservable();
  readonly previewError = this.previewError$.asObservable();
  readonly errorDescription = this.errorDescription$.asObservable();
  readonly errorTerminalOutput = this.errorTerminalOutput$.asObservable();
  readonly currentTheme = this.currentTheme$.asObservable();
  readonly deployedUrl = this.deployedUrl$.asObservable();
  readonly isPanelCollapsed = this.isPanelCollapsed$.asObservable();
  readonly isLoading = this.isLoading$.asObservable();
  readonly isHistoryActive = this.isHistoryActive$.asObservable();
  readonly isCodeActive = this.isCodeActive$.asObservable();
  readonly isPreviewActive = this.isPreviewActive$.asObservable();
  readonly isLogsActive = this.isLogsActive$.asObservable();
  readonly isArtifactsActive = this.isArtifactsActive$.asObservable();
  readonly isLeftPanelCollapsed = this.isLeftPanelCollapsed$.asObservable();
  readonly minWidth = this.minWidth$.asObservable();
  readonly isExperienceStudioModalOpen = this.isExperienceStudioModalOpen$.asObservable();

  // Project name properties
  projectName: string | null = null;
  isProjectNameLoading: boolean = true;

  // Artifacts tab properties
  isArtifactsTabEnabled: boolean = false; // Initially disabled until PROJECT_OVERVIEW state is received
  // Only include Project Overview and README initially
  // Layout Analyzed and Design System will be added when their states are available
  artifactsData: any[] = [
    {
      name: 'Project Overview',
      type: 'markdown',
      content: '# Project Overview\n\nWe are getting the project overview ready for you.'
    },
  ];

  // Flags to track if specific artifacts are available
  hasLayoutAnalyzed: boolean = false;
  hasDesignSystem: boolean = false;
  selectedArtifactFile: any = null;

  // Design system data
  designSystemData: any = null;
  designTokens: DesignToken[] = [];
  layoutAnalyzedData: any[] = [];

  // Progress state tracking
  currentProgressState: string = '';

  lastProgressDescription: string = '';
  pollingStatus: string = 'PENDING';

  private autoSwitchToLogsTimer: any;

  // Default layout example images (fallback)
  layoutExampleImages: string[] = [
    'assets/images/01.png',
    'assets/images/02.png',
    'assets/images/03.png',
    'assets/images/04.png',
    'assets/images/05.png',
    'assets/images/06.png',
    'assets/images/07.png',
    'assets/images/08.png',
  ];

  // Layout mapping
  layoutMapping: { [key: string]: string } = {
    HB: 'Header + Body (No Sidebars, No Footer)',
    HBF: 'Header + Body + Footer (No Sidebars)',
    HLSB: 'Header + Left Sidebar + Body (No Footer)',
    HLSBF: 'Header + Left Sidebar + Body + Footer',
    HBRS: 'Header + Body + Right Sidebar (No Footer)',
    HBRSF: 'Header + Body + Right Sidebar + Footer',
    HLSBRS: 'Header + Left Sidebar + Body + Right Sidebar (No Footer)',
    HLSBRSF: 'Header + Left Sidebar + Body + Right Sidebar + Footer',
  };

  // Layout data directly from API response
  layoutData: string[] = ['HB', 'HBF'];

  // Loading state for layout cards
  isLayoutLoading: boolean = true;

  // Selected image data URI
  selectedImageDataUri: string = '';

  // Chat messages
  lightMessages: {
    text: string;
    from: 'user' | 'ai';
    theme: 'light';
    hasSteps?: boolean;
    imageDataUri?: string;
    isError?: boolean;
    errorDetails?: string;
    isErrorExpanded?: boolean;
  }[] = [];

  darkMessages: {
    text: string;
    from: 'user' | 'ai';
    theme: 'dark';
  }[] = [];

  lightPrompt: string = '';
  darkPrompt: string = '';
  // Status checking properties
  projectId: string | null = null;
  jobId: string | null = null;
  isPolling = false;
  isCodeGenerationComplete = false; // Track if code generation is complete
  isCodeTabEnabled = false; // Track if code tab is enabled
  isPreviewTabEnabled = false; // Track if preview tab is enabled
  isLogsTabEnabled = false; // Track if logs tab is enabled
  image = 'assets/images/history_card.png';
  title = 'Version 1 Application';
  appName: string | null = null; // Store the generated app name

  // Logs properties
  hasLogs: boolean = false;
  logMessages: string[] = [];
  formattedLogMessages: any[] = []; // Array to hold formatted logs with type, content, and path
  isStreamingLogs: boolean = false;
  isTypingLog: boolean = false; // Made public for template access
  currentLogIndex: number = 0; // Made public for template access
  private logStreamTimer: any;
  private currentCharIndex: number = 0;
  private processedLogHashes: Set<string> = new Set(); // Track processed logs by hash to avoid duplicates
  private lastProgressState: string = ''; // Track the last progress state to only show changes
  private expandedCodeLogs: Set<string> = new Set(); // Track which code logs are expanded

  // Enhanced typewriter properties for logs
  private logTypewriterInstance?: string;
  private logTypewriterInstances: Map<string, string> = new Map(); // Map log ID to typewriter instance

  // Enhanced typewriter properties for artifacts
  private artifactTypewriterInstances: Map<string, string> = new Map(); // Map artifact name to typewriter instance
  public artifactVisibleContent: { [key: string]: string } = {}; // Track visible content for each artifact

  // Loading state messages
  loadingMessages = [
    'Analyzing your requirements and design specifications... 🔍',
    'Identifying key UI/UX patterns from your requirements... 🧩',
    'Mapping component relationships and dependencies... 🗺️',
    'Generating component architecture and file structure... 📂',
    'Planning optimal folder organization for scalability... 📁',
    'Creating responsive UI components with modern best practices... 🎨',
    'Implementing accessibility standards for inclusive design... ♿',
    'Building reusable component library for consistency... 🧰',
    'Implementing interactive elements and event handlers... 🖱️',
    'Adding form validation and user input handling... ✅',
    'Optimizing code for performance and maintainability... ⚙️',
    'Implementing lazy loading for improved initial load time... ⚡',
    'Adding CSS styling and layout configurations... 💅',
    'Creating responsive breakpoints for all device sizes... 📱',
    'Implementing data flow and state management... 🔄',
    'Setting up API integration and data fetching logic... 🔌',
    'Configuring error handling and fallback states... 🛡️',
    'Ensuring cross browser compatibility and responsive design... 🌐',
    'Implementing animations and transitions for better UX... ✨',
    'Optimizing assets and resources for faster loading... 🚄',
    'Finalizing code with proper documentation and comments... 📝',
    'Running code quality checks and optimizations... 🧹',
    'Preparing preview deployment for your application... 🚀',
    'Setting up build configuration for optimal performance... 🏗️',
    'Finalizing application bundle for deployment... 📦',
  ];

  /**
   * Transform loading messages to remove underscores and hyphens
   * This method is called in the constructor
   */
  private transformLoadingMessages(): void {
    // Apply text transformation to any messages that might still have underscores or hyphens
    this.loadingMessages = this.textTransformationService.transformMessages(this.loadingMessages);
  }

  generatedCode: any;
  editorReady = false;
  private subscription: Subscription | null = null;

  // Element selection properties
  selectedElement: any = null;
  showEditorIcon = false;
  urlSafe?: SafeResourceUrl;
  // Element selection class - commented out since it's not currently used
  // private lastSelectedElementClass = 'element-highlight';
  isElementSelectionMode = false;
  selectedElementHTML: string | null = null;
  selectedElementCSS: string | null = null;
  selectedElementPath: string | null = null;
  selectedElementId: string | null = null;
  selectedElementTagName: string | null = null;

  // Image preview properties
  showPreview: boolean = false;
  previewImage: { url: string, name: string } | null = null;

  // Reference to the chat-window component
  @ViewChild(ChatWindowComponent) chatWindow!: ChatWindowComponent;

  constructor(
    private codeSharingService: CodeSharingService,
    private codeGenerationService: CodeGenerationService,
    private themeService: ThemeService,
    private cdr: ChangeDetectorRef,
    // Renderer2 is kept for potential future DOM manipulations but currently unused
    // private renderer2: Renderer2,
    public sanitizer: DomSanitizer,
    private pollingService: PollingService,
    private appStateService: AppStateService,
    private ngZone: NgZone,
    private promptService: PromptBarService,
    private promptSubmissionService: PromptSubmissionService,
    private router: Router,
    private userSignatureService: UserSignatureService,
    private textTransformationService: TextTransformationService,
    private stepperStateService: StepperStateService,
    private toastService: ToastService,
    private typewriterService: TypewriterService
  ) {
    // Initialize current theme using BehaviorSubject
    this.currentTheme$.next(this.themeService.getCurrentTheme());
    // Initialize icons based on the current theme

    // Transform loading messages to remove underscores and hyphens
    this.transformLoadingMessages();
  }

  /**
   * Navigate to the home/main page and reset all state
   */
  navigateToHome(): void {
    this.logger.info('Navigating to home and resetting all state');

    // Reset logs state first
    this.pollingService.resetLogs();

    // Stop any active polling
    if (this.isPolling) {
      this.pollingService.stopPolling();
      this.isPolling = false;
    }

    // Reset the app state
    this.appStateService.resetProjectState();

    // Reset the prompt submission state
    this.promptSubmissionService.resetSubmissionState();

    // Reset the code sharing service state
    this.codeSharingService.resetState();

    // Clear any selected files or images in the prompt service
    this.promptService.setImage(null);
    this.promptService.setPrompt('');

    // Reset local component state
    this.resetComponentState();

    // Clear any existing toasts before showing a new one
    this.toastService.clear();

    // Show toast notification
    this.toastService.info('Returning to home page');

    // Navigate to the home page
    this.router.navigate(['/experience']);
  }

  /**
   * Reset all component state to initial values
   * This is a comprehensive reset of all local component state
   */
  private resetComponentState(): void {
    // Reset file and code state using BehaviorSubjects
    this.files$.next([]);
    this.isCodeGenerationComplete = false;
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);
    this.layoutData = [];
    this.generatedCode = null;
    this.editorReady = false;

    // Reset logs state
    this.logMessages = [];
    this.formattedLogMessages = [];
    this.hasLogs = false;
    this.isStreamingLogs = false;
    this.isTypingLog = false;
    this.currentLogIndex = 0;
    this.currentCharIndex = 0;
    this.processedLogHashes.clear();
    this.lastProgressState = '';
    this.expandedCodeLogs.clear();

    // Clean up typewriter instances
    this.cleanupTypewriterInstances();

    // Reset UI state using BehaviorSubjects
    this.selectedImageDataUri = '';
    this.currentProgressState = '';
    this.lastProgressDescription = '';
    this.pollingStatus = 'PENDING';
    this.userSelectedTab = false;
    this.isResizing$.next(false);
    this.isPanelCollapsed$.next(false);
    this.isLoading$.next(true);
    this.isHistoryActive$.next(false);
    this.isLeftPanelCollapsed$.next(false);
    this.isExperienceStudioModalOpen$.next(false);

    // Reset tab states using BehaviorSubjects
    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(false);
    this.isPreviewTabEnabled = true;
    this.isLogsTabEnabled = true;
    this.isCodeTabEnabled = false;
    this.isArtifactsTabEnabled = false;

    // Reset view state using BehaviorSubject
    this.currentView$.next('preview');

    // Reset chat messages and prompt state
    this.lightMessages = [];
    this.darkMessages = [];
    this.lightPrompt = '';
    this.darkPrompt = '';

    // Clear any selected image in chat window
    this.selectedImageDataUri = '';

    // Reset error state using BehaviorSubjects
    this.previewError$.next(false);
    this.errorDescription$.next('Even the best systems have bad days sometimes. Please try again later.');
    this.errorTerminalOutput$.next('');

    // Reset project state
    this.projectId = null;
    this.jobId = null;
    this.appName = null;
    this.projectName = null;
    this.isProjectNameLoading = true;

    // Reset element selection state
    this.selectedElement = null;
    this.showEditorIcon = false;
    this.isElementSelectionMode = false;
    this.selectedElementHTML = null;
    this.selectedElementCSS = null;
    this.selectedElementPath = null;
    this.selectedElementId = null;
    this.selectedElementTagName = null;

    // Reset any timers
    if (this.autoSwitchToLogsTimer) {
      clearTimeout(this.autoSwitchToLogsTimer);
      this.autoSwitchToLogsTimer = null;
    }

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
      this.logStreamTimer = null;
    }

    // Clear design tokens update timer
    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
      this.designTokensUpdateTimer = null;
    }
  }

  /**
   * Opens the preview in a new browser tab
   */
  openPreviewInNewTab(): void {
    const currentUrl = this.deployedUrl$.value;
    if (currentUrl) {
      window.open(currentUrl, '_blank');
      this.toastService.info('Opening preview in new tab');
    } else {
      this.toastService.warning('Preview URL is not available yet');
    }
  }

  /**
   * Shows the image preview overlay
   * @param imageUrl URL of the image to preview
   * @param imageName Name of the image (optional)
   */
  showImagePreview(imageUrl: string, imageName: string = 'Image'): void {
    this.previewImage = { url: imageUrl, name: imageName };
    this.showPreview = true;
    this.cdr.detectChanges();
  }

  /**
   * Closes the image preview overlay
   */
  closeImagePreview(): void {
    this.showPreview = false;
    this.previewImage = null;
    this.cdr.detectChanges();
  }

  /**
   * Clean up all typewriter instances
   */
  private cleanupTypewriterInstances(): void {
    // Clean up batch typewriter instance
    if (this.logTypewriterInstance) {
      this.typewriterService.stop(this.logTypewriterInstance);
      this.logTypewriterInstance = undefined;
    }

    // Clean up individual log typewriter instances
    this.logTypewriterInstances.forEach(instanceId => {
      this.typewriterService.stop(instanceId);
    });
    this.logTypewriterInstances.clear();

    // Clean up artifact typewriter instances
    this.artifactTypewriterInstances.forEach(instanceId => {
      this.typewriterService.stop(instanceId);
    });
    this.artifactTypewriterInstances.clear();
    this.artifactVisibleContent = {};
  }

  /**
   * Generates an app name based on job ID and project ID and sets the deployed URL
   */
  generateAndSetAppName(): Promise<void> {
    return new Promise<void>(resolve => {
      if (!this.jobId || !this.projectId) {
        resolve();
        return;
      }

      // Get the user ID from the app state service
      const appState = this.appStateService.getProjectState();

      // Make sure we have a valid user ID or fall back to job ID
      let userId = appState.userId;

      // Check if userId is null, undefined, or empty string
      if (!userId || userId.trim() === '') {
        userId = this.jobId;
      }

      // Create folder path using user ID instead of job ID
      const folderPath = `users/${userId}/${this.projectId}`;

      try {
        // Generate the app name (no longer async)
        this.appName = generateAppName(folderPath);

        // Create the Azure website URL
        const azureUrl = `https://${this.appName}.azurewebsites.net`;

        // Set the deployed URL in the service and update the component
        this.codeSharingService.setDeployedUrl(azureUrl);
        this.deployedUrl$.next(azureUrl);

        // Sanitize the URL for the iframe
        this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(azureUrl);

        // Update UI state using BehaviorSubjects
        this.isPreviewLoading$.next(false);
        this.previewIcon$.next('bi-eye');
        this.isPreviewTabEnabled = true;

        // Force a change detection cycle
        this.cdr.detectChanges();
        // Don't show toast here as it's handled by the polling service
        resolve();
      } catch (error) {
        this.logger.error('Error generating app name:', error);
        this.toastService.error('Failed to generate preview URL');
        resolve();
      }
    });
  }

  /**
   * Process artifact data from the polling service
   * @param artifactData The artifact data to process
   */
  private processArtifactData(artifactData: any): void {
    if (!artifactData) return;

    this.logger.info('Processing artifact data:', artifactData);

    // Check if the artifact data has a 'data' field
    if (artifactData.data) {
      // Get the current progress state
      const currentState = this.currentProgressState;

      // Process the data based on the current progress state
      if (currentState === StepperState.OVERVIEW || !currentState) {
        // Enable the Artifacts tab when Project Overview data is received
        this.isArtifactsTabEnabled = true;
        this.logger.info('Artifacts tab enabled due to Project Overview data');

        // Update the Project Overview artifact
        const projectOverviewIndex = this.artifactsData.findIndex(item => item.name === 'Project Overview');
        if (projectOverviewIndex !== -1) {
          // Update existing Project Overview
          this.artifactsData[projectOverviewIndex].content = artifactData.data;
          this.logger.info('Updated Project Overview artifact with new data');
        } else {
          // Add new Project Overview
          this.artifactsData.push({
            name: 'Project Overview',
            type: 'markdown',
            content: artifactData.data
          });
          this.logger.info('Added new Project Overview artifact');
        }
      } else if (currentState === StepperState.LAYOUT_ANALYZED) {
        // Update or add Layout Analyzed artifact
        const layoutAnalyzedIndex = this.artifactsData.findIndex(item => item.name === 'Layout Analyzed');
        if (layoutAnalyzedIndex !== -1) {
          // Update existing Layout Analyzed
          this.artifactsData[layoutAnalyzedIndex].content = artifactData.data;
          this.logger.info('Updated Layout Analyzed artifact with new data');
        } else {
          // Add new Layout Analyzed
          this.artifactsData.push({
            name: 'Layout Analyzed',
            type: 'image',
            content: artifactData.data
          });
          this.logger.info('Added new Layout Analyzed artifact');
          this.hasLayoutAnalyzed = true;
        }
      } else if (currentState === StepperState.DESIGN_SYSTEM_MAPPED) {
        // Update or add Design System artifact
        const designSystemIndex = this.artifactsData.findIndex(item => item.name === 'Design System');
        if (designSystemIndex !== -1) {
          // Update existing Design System
          this.artifactsData[designSystemIndex].content = artifactData.data;
          this.logger.info('Updated Design System artifact with new data');
        } else {
          // Add new Design System
          this.artifactsData.push({
            name: 'Design System',
            type: 'component',
            content: artifactData.data
          });
          this.logger.info('Added new Design System artifact');
          this.hasDesignSystem = true;
        }
      } else {
        // For other states, add a generic artifact with the state name
        const stateName = this.getDisplayNameForState(currentState);
        const stateIndex = this.artifactsData.findIndex(item => item.name === stateName);
        if (stateIndex !== -1) {
          // Update existing artifact
          this.artifactsData[stateIndex].content = artifactData.data;
          this.logger.info(`Updated ${stateName} artifact with new data`);
        } else {
          // Add new artifact
          this.artifactsData.push({
            name: stateName,
            type: 'markdown',
            content: artifactData.data
          });
          this.logger.info(`Added new ${stateName} artifact`);
        }
      }

      // Force change detection to update the UI
      this.cdr.detectChanges();
    }
  }

  /**
   * Get a display name for a progress state
   * @param state The progress state
   * @returns A display name for the state
   */
  private getDisplayNameForState(state: string): string {
    // Convert the state to a more readable format
    // e.g., LAYOUT_ANALYZED -> Layout Analyzed
    if (!state) return 'Unknown State';

    return state
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }

  ngOnInit() {
    // Reset component state
    this.resetComponentState();

    // Initialize with default prompt if no prompt is available
    this.initializeDefaultPrompt();

    // Initialize app state and subscriptions
    this.initializeAppState();

    // Select the first artifact file by default (will be shown when artifacts tab is enabled)
    if (this.artifactsData.length > 0 && !this.selectedArtifactFile) {
      this.selectArtifactFile(this.artifactsData[0]);
    }

    // Subscribe to artifact data from polling service
    if (this.subscription) {
      this.subscription.add(
        this.pollingService.artifactData$.subscribe(artifactData => {
          if (artifactData) {
            this.processArtifactData(artifactData);
          }
        })
      );
    } else {
      // If subscription is null, create a new one
      this.subscription = this.pollingService.artifactData$.subscribe(artifactData => {
        if (artifactData) {
          this.processArtifactData(artifactData);
        }
      });
    }

    // Simulate loading the project name with shimmer effect
    this.isProjectNameLoading = true;
    setTimeout(() => {
      this.projectName = 'Generated Application';
      this.isProjectNameLoading = false;
      this.cdr.detectChanges();
    }, 2000); // Show shimmer for 2 seconds before displaying the project name
  }

  /**
   * After view initialization, connect the chat-window's showImagePreview method to our code-window's showImagePreview method
   */
  ngAfterViewInit(): void {
    // Use setTimeout to ensure the chat-window component is fully initialized
    setTimeout(() => {
      // Override the chat-window's showImagePreview method to use our full-screen overlay
      if (this.chatWindow) {
        // Override the method
        this.chatWindow.showImagePreview = (imageUrl: string, imageName: string = 'Image') => {
          // Call our showImagePreview method instead
          this.showImagePreview(imageUrl, imageName);
        };
      }
    }, 0);

    this.setupPanelWidthObserver();
  }

    /**
   * Set up observer to monitor left panel width and hide project name when too narrow
   */
  private setupPanelWidthObserver(): void {
    // Use ResizeObserver to monitor the left panel width
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const width = entry.contentRect.width;
          // Hide project name when panel width is less than 400px
          this.shouldHideProjectName$.next(width < 400);
          this.cdr.detectChanges();
        }
      });

      // Observe the left panel element
      setTimeout(() => {
        const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
        if (leftPanel) {
          resizeObserver.observe(leftPanel);
        }
      }, 100);
    } else {
      // Fallback for browsers that don't support ResizeObserver
      // Use window resize event as a fallback
      const checkPanelWidth = () => {
        const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
        if (leftPanel) {
          const width = leftPanel.offsetWidth;
          this.shouldHideProjectName$.next(width < 400);
          this.cdr.detectChanges();
        }
      };

      // Check on window resize
      window.addEventListener('resize', checkPanelWidth);

      // Initial check
      setTimeout(checkPanelWidth, 100);
    }
  }

  /**
   * Subscribe to app state and initialize component
   */
  private initializeAppState(): void {
    // Subscribe to the app state to get project and job IDs
    this.subscription = this.appStateService.project$.subscribe(projectState => {
      // Update project ID and job ID if they've changed
      if (projectState.projectId && projectState.projectId !== this.projectId) {
        // If we have a new project ID, update the stepper state service
        if (this.projectId) {
          // This means we're switching from one project to another
          // Trigger a stepper reset
          this.stepperStateService.triggerStepperReset();
        }

        this.projectId = projectState.projectId;

        // Update the stepper state service with the new project ID
        this.stepperStateService.setCurrentProjectId(this.projectId);
      }

      if (projectState.jobId && projectState.jobId !== this.jobId) {
        this.jobId = projectState.jobId;

        // If we have both project ID and job ID, generate the app name for the iframe URL
        if (this.projectId && this.jobId) {
          this.generateAndSetAppName();
        }
      }

      // Initialize chat with user prompt if available and we haven't done it yet
      if (projectState.prompt && this.lightMessages.length === 0) {
        // Add the user prompt to the chat messages
        this.lightMessages = [
          { text: projectState.prompt, from: 'user', theme: 'light' },
          {
            text: "I'm generating code based on your request. Please wait while I process your input...",
            from: 'ai',
            theme: 'light',
          },
        ];
      }

      // If we have both IDs and polling hasn't started, start it
      if (this.projectId && this.jobId && !this.isPolling) {
        this.pollingService.startPolling(this.projectId, this.jobId, {
          taskType: 'code generation',
          // Using fixed 5-second polling interval
        });
        this.updatePollingStatus(true);

        // Generate the app name for the iframe URL if not already done
        if (!this.appName) {
          this.generateAndSetAppName();
        }
      }
    });

    // No need for interval check anymore as we're using the AppStateService

    // Show preview tab initially with loading animation using BehaviorSubjects
    this.currentView$.next('preview');
    this.isLoading$.next(true);
    this.isPreviewLoading$.next(true);
    this.isPreviewActive$.next(true);
    this.isCodeActive$.next(false);
    this.isLogsActive$.next(false);

    // Only enable preview and logs tabs initially
    this.isLogsTabEnabled = true;
    this.isPreviewTabEnabled = true;
    this.isCodeTabEnabled = false; // Code tab will be enabled when code is generated

    // Check if we already have project ID and job ID to generate the app name
    if (this.projectId && this.jobId) {
      // Generate app name and set the deployed URL
      this.generateAndSetAppName();
    } else {
      // Use the hardcoded URL as fallback
      this.deployedUrl$.next('https://orange-island-09111c10f.6.azurestaticapps.net/');

      // Sanitize the deployed URL for iframe
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value || '');
    }

    // Subscribe to polling service status updates
    this.subscription = this.pollingService.status$.subscribe(status => {
      // Update the polling status for the stepper
      const newStatus = status === 'completed' ? 'COMPLETED' :
        status === 'failed' || status === 'error' ? 'FAILED' : 'IN_PROGRESS';

      // Only update if the status has changed to avoid unnecessary updates
      if (this.pollingStatus !== newStatus) {
        this.logger.info(`Updating polling status from ${this.pollingStatus} to ${newStatus}`);
        this.pollingStatus = newStatus;

        // If the status is 'completed', make sure we have the correct artifacts
        if (status === 'completed') {
          this.logger.info('Status is completed, checking artifacts');

          // We don't want to remove artifacts when the status is completed
          // Instead, we'll just log the current state
          this.logger.info('Layout analyzed flag:', this.hasLayoutAnalyzed);
          this.logger.info('Design system flag:', this.hasDesignSystem);

          // Check if we have the artifacts in the list
          const hasLayoutAnalyzedFile = this.artifactsData.some(file => file.name === 'Layout Analyzed');
          const hasDesignSystemFile = this.artifactsData.some(file => file.name === 'Design System');

          this.logger.info('Layout analyzed in artifacts:', hasLayoutAnalyzedFile);
          this.logger.info('Design system in artifacts:', hasDesignSystemFile);
        }

        // Handle FAILED state - show error page in preview
        if (newStatus === 'FAILED') {
          this.logger.info('Detected FAILED state, showing error page in preview');
          this.isLoading$.next(false);
          this.isPreviewLoading$.next(false);
          this.previewError$.next(true);

          // We need to set isCodeGenerationComplete to true to show the error page,
          // but we don't want to show the code tab when there's an error
          this.isCodeGenerationComplete = true; // Mark as complete to show error page

          // Ensure code tab is not active when there's an error using BehaviorSubjects
          this.isCodeActive$.next(false);
          this.isPreviewActive$.next(true);
          this.isLogsActive$.next(false);

          // Get the error message from the polling service
          // This will extract the error message from the latest status response
          this.extractErrorMessageFromProgressDescription();

          // Switch to preview view to show the error page
          if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
            this.logger.info('Auto-switching to preview tab to show error page');
            this.togglePreviewView();
            // Reset the flag since this was an automatic switch, not user-initiated
            this.userSelectedTab = false;
          } else {
            // Force the current view to be preview
            this.currentView$.next('preview');
          }
        }
      }



      if (status === 'completed') {
        this.isLoading$.next(false);
        this.isPreviewLoading$.next(false); // Ensure preview loading is also disabled

        // Only mark code generation as complete and enable code tab if there's no error
        if (!this.previewError$.value) {
          this.isCodeGenerationComplete = true; // Set the flag to indicate code generation is complete
          this.isCodeTabEnabled = true; // Enable the code tab
          this.logger.info('Code generation completed successfully, enabling code tab');

          // Clear any existing toasts before showing a new one
          this.toastService.clear();

          // Show success toast notification only if we're not navigating away
          if (this.router.url.includes('/experience/generate')) {
            this.toastService.success('Code generation completed successfully. Preview is ready.');
          }
        } else {
          this.logger.info('Code generation completed with errors, code tab remains disabled');
        }

        // Empty the layoutData array when state is finished
        this.layoutData = [];

        // Switch to preview view only if user hasn't manually selected a tab
        if (!this.userSelectedTab) {
          this.togglePreviewView();
        } else {
          // Still need to update the tab states without changing the view using BehaviorSubjects
          this.isCodeActive$.next(false);
          this.isPreviewActive$.next(true);
          this.isLogsActive$.next(false);
        }

        // Force check if iframe exists after a short delay
        setTimeout(() => {
          const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
          if (!iframe) {
            this.logger.warn(
              'Iframe not found in DOM after status completed, forcing reload of preview view'
            );
            // Only force a refresh of the view if we're already in preview view
            if (this.currentView$.value === 'preview') {
              this.currentView$.next('editor');
              setTimeout(() => {
                this.currentView$.next('preview');
              }, 50);
            } else {
              this.logger.info('Not forcing refresh since we are not in preview view');
            }
          }
        }, 200);

        // Add a message to the chat when code generation is completed
      } else if (status === 'failed' || status === 'error') {
        this.isLoading$.next(false);
        this.isPreviewLoading$.next(false); // Ensure preview loading is also disabled
        this.previewError$.next(true);
        this.isCodeGenerationComplete = true; // Mark as complete to show error page
        this.isCodeTabEnabled = false; // Disable the code tab when there's an error

        // Clear any existing toasts before showing a new one
        this.toastService.clear();

        // Show error toast notification only if we're not navigating away
        if (this.router.url.includes('/experience/generate')) {
          this.toastService.error('Code generation failed.');
        }

        // Ensure code tab is not active when there's an error using BehaviorSubjects
        this.isCodeActive$.next(false);
        this.isPreviewActive$.next(true);
        this.isLogsActive$.next(false);

        // Extract error message from progress description
        // This will extract the error message from the latest status response
        this.extractErrorMessageFromProgressDescription();

        // Switch to preview view to show the error page
        if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
          this.logger.info('Auto-switching to preview tab to show error page');
          this.togglePreviewView();
          // Reset the flag since this was an automatic switch, not user-initiated
          this.userSelectedTab = false;
        } else {
          // Force the current view to be preview
          this.currentView$.next('preview');
        }
      }
    });

    // Subscribe to polling service progress updates
    this.subscription.add(
      this.pollingService.progress$.subscribe(progress => {
        if (progress) {
          // Add a progress message to the chat if it's not empty
          this.logger.info('Polling progress updated:', progress);

          // Update the loading message with the progress
          const lastMessage = this.lightMessages[this.lightMessages.length - 1];
          if (
            lastMessage &&
            lastMessage.from === 'ai' &&
            lastMessage.text.includes('generating code')
          ) {
            // Update the last message instead of adding a new one
            lastMessage.text = progress;
          }

          // Check for specific progress states and update artifacts accordingly
          if (progress === StepperState.LAYOUT_ANALYZED) {
            this.logger.info('LAYOUT_ANALYZED progress state detected');
            // Set the flag that layout analyzed data is available
            this.hasLayoutAnalyzed = true;
            // Capture the layout analyzed state permanently in artifacts
            this.captureLayoutAnalyzedState();
          } else if (progress === StepperState.DESIGN_SYSTEM_MAPPED) {
            this.logger.info('DESIGN_SYSTEM_MAPPED progress state detected');
            // Set the flag that design system data is available
            this.hasDesignSystem = true;
            // Capture the design system mapped state permanently in artifacts
            this.captureDesignSystemMappedState();
          }

          // Update the loading messages with the progress
          // this.loadingMessages = [...this.loadingMessages.slice(0, 4), progress];

          // Track the current progress state
          this.currentProgressState = progress;
          // Only disable loading when we reach specific states AND have valid layout data
          if (
            (progress === StepperState.LAYOUT_ANALYZED ||
              progress === StepperState.PAGES_GENERATED ||
              progress === StepperState.COMPONENTS_CREATED ||
              progress === StepperState.BUILD_SUCCEEDED) &&
            this.layoutData &&
            this.layoutData.length > 0 &&
            this.layoutMapping[this.layoutData[0]]
          ) {
            this.isPreviewLoading$.next(false);
            this.isLoading$.next(false);
          } else if (
            !this.layoutData ||
            this.layoutData.length === 0 ||
            !this.layoutMapping[this.layoutData[0]]
          ) {
            // If we don't have valid layout data, ensure loading is shown
            this.isPreviewLoading$.next(true);
            this.isLoading$.next(true);
            this.logger.info('No valid layout data, showing loading screen');
          }

          // Handle specific progress states
          if (
            progress === StepperState.LAYOUT_ANALYZED ||
            progress === StepperState.DESIGN_SYSTEM_MAPPED
          ) {
            this.logger.info('Layout analyzed or Design System mapped state detected:', progress);

            // Clear any existing toasts before showing a new one
            this.toastService.clear();

            // Clear any existing timer
            if (this.autoSwitchToLogsTimer) {
              clearTimeout(this.autoSwitchToLogsTimer);
            }

            // Handle specific states
            if (progress === StepperState.LAYOUT_ANALYZED) {
              // Set the flag that layout analyzed data is available
              this.hasLayoutAnalyzed = true;
            } else if (progress === StepperState.DESIGN_SYSTEM_MAPPED) {
              // Set the flag that design system data is available
              this.hasDesignSystem = true;
            }

            // If we don't have layout data, keep it empty and show loading
            if (
              !this.layoutData ||
              this.layoutData.length === 0 ||
              !this.layoutMapping[this.layoutData[0]]
            ) {
              // Don't use default fallback
              this.isLayoutLoading = true;
              this.isPreviewLoading$.next(true);
              this.isLoading$.next(true);
            } else {
              // If we have valid layout data, hide loading
              this.isLayoutLoading = false;
              this.isPreviewLoading$.next(false);
              this.isLoading$.next(false);
            }

            // Process multiple layouts if present
            if (this.layoutData && this.layoutData.length > 0) {
              this.logger.info(`${this.layoutData.length} layout(s) detected, using all valid layouts`);
              // Keep all valid layouts instead of just the first one
              this.layoutData = this.layoutData.filter(key => this.layoutMapping[key]);

              // Log the image paths for each layout key
              if (this.layoutData.length > 0) {
                this.layoutData.forEach(_key => {
                  // Placeholder for future implementation if needed
                });
              }
            }
          } else if (progress === StepperState.PAGES_GENERATED) {
            this.logger.info('Pages generated state detected');

            // Clear any existing toasts before showing a new one
            this.toastService.clear();


            // Clear any existing timer
            if (this.autoSwitchToLogsTimer) {
              clearTimeout(this.autoSwitchToLogsTimer);
            }



            // If we don't have layout data, keep it empty and show loading
            if (
              !this.layoutData ||
              this.layoutData.length === 0 ||
              !this.layoutMapping[this.layoutData[0]]
            ) {
              this.logger.info(
                'No layout data detected in PAGES_GENERATED state, keeping empty array and showing loading'
              );
              // Don't use default fallback
              this.isLayoutLoading = true;
              this.isPreviewLoading$.next(true);
              this.isLoading$.next(true);
            } else {
              // If we have valid layout data, hide loading after a short delay
              this.isLayoutLoading = true;

              // Set a timeout to simulate loading for 1 second
              setTimeout(() => {
                this.isLayoutLoading = false;
                this.isPreviewLoading$.next(false);
                this.isLoading$.next(false);
              }, 1000);
            }

            // Set timer to switch to logs tab after 20 seconds, but only if user hasn't manually selected a tab
            this.autoSwitchToLogsTimer = setTimeout(() => {
              if (!this.userSelectedTab) {
                this.logger.info(
                  'Auto-switching to logs tab after 20 seconds (user has not manually selected a tab)'
                );
                this.toggleLogsView();
                // Reset the flag since this was an automatic switch, not user-initiated
                this.userSelectedTab = false;
              } else {
                this.logger.info('Respecting user tab selection, not auto-switching to logs');
              }
            }, 20000); // 20 seconds
          } else if (progress === StepperState.BUILD_STARTED) {
            this.logger.info('Build started state detected');

            // Clear any existing toasts before showing a new one
            this.toastService.clear();


            // Clear any existing timer
            if (this.autoSwitchToLogsTimer) {
              clearTimeout(this.autoSwitchToLogsTimer);
            }

            // Switch to logs view to show build progress
            if (this.currentView$.value !== 'logs' && !this.userSelectedTab) {
              this.logger.info(
                'Auto-switching to logs tab for build progress (user has not manually selected a tab)'
              );
              this.toggleLogsView();
              // Reset the flag since this was an automatic switch, not user-initiated
              this.userSelectedTab = false;
            }
          } else if (progress === StepperState.BUILD_SUCCEEDED) {
            this.logger.info('Build succeeded state detected');

            // Clear any existing toasts before showing a new one
            this.toastService.clear();

            // Show toast notification for build success only if we're not navigating away
            if (this.router.url.includes('/experience/generate')) {
              this.toastService.success('Application successfully built.');
            }

            // Switch to preview view to show the final result
            if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
              this.logger.info(
                'Auto-switching to preview tab for successful build (user has not manually selected a tab)'
              );
              this.togglePreviewView();
              // Reset the flag since this was an automatic switch, not user-initiated
              this.userSelectedTab = false;
            }

            // Only enable the code tab if there's no error
            if (!this.previewError$.value) {
              this.isCodeTabEnabled = true;
              this.isCodeGenerationComplete = true;
              this.logger.info('Build succeeded, enabling code tab');
            } else {
              this.logger.info('Build succeeded but errors exist, code tab remains disabled');
            }
          } else if (progress === StepperState.BUILD_FAILED) {
            this.logger.info('Build failed state detected');

            // Clear any existing toasts before showing a new one
            this.toastService.clear();

            // Show toast notification for build failure only if we're not navigating away
            if (this.router.url.includes('/experience/generate')) {
              this.toastService.error('Build got failed.');
            }

            // Set error state using BehaviorSubjects
            this.isLoading$.next(false);
            this.isPreviewLoading$.next(false);
            this.previewError$.next(true);
            this.isCodeGenerationComplete = true; // Mark as complete to show error page

            // Ensure code tab is not active when there's an error using BehaviorSubjects
            this.isCodeActive$.next(false);
            this.isPreviewActive$.next(true);
            this.isLogsActive$.next(false);

            // Extract error message from progress description
            // This will extract the error message from the latest status response
            this.extractErrorMessageFromProgressDescription();

            // Switch to preview view to show the error page
            if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
              this.logger.info('Auto-switching to preview tab to show error page');
              this.togglePreviewView();
              // Reset the flag since this was an automatic switch, not user-initiated
              this.userSelectedTab = false;
            } else {
              // Force the current view to be preview
              this.currentView$.next('preview');
            }
          } else if (progress === StepperState.OVERVIEW) {
            this.logger.info('Project Overview state detected');

            // Enable the Artifacts tab when Project Overview is complete
            this.isArtifactsTabEnabled = true;

            // Add Project Overview to artifacts data if not already present
            const hasOverview = this.artifactsData.some(file => file.name === 'Project Overview');
            if (!hasOverview) {
              // Get the project overview content from the last progress description
              let overviewContent = this.lastProgressDescription;
              if (!overviewContent) {
                overviewContent = '# Project Overview\n\nProject overview information will be displayed here.';
              }

              this.artifactsData.push({
                name: 'Project Overview',
                type: 'markdown',
                content: overviewContent
              });

              // Select the Project Overview file by default
              if (this.artifactsData.length === 1) {
                this.selectArtifactFile(this.artifactsData[0]);
              }
            }

            // Auto-switch to artifacts tab if user hasn't manually selected a tab
            if (!this.userSelectedTab) {
              this.logger.info('Auto-switching to artifacts tab after Project Overview completion');
              this.toggleArtifactsView();
              // Reset the flag since this was an automatic switch, not user-initiated
              this.userSelectedTab = false;
            }

            // If user hasn't selected a tab, switch to artifacts view
            if (!this.userSelectedTab) {
              this.toggleArtifactsView();
              // Reset the flag since this was an automatic switch
              this.userSelectedTab = false;
            }
          } else if (progress === StepperState.LAYOUT_ANALYZED) {
            this.logger.info('Layout Analyzed state detected');

            // Capture the layout analyzed state permanently in artifacts
            this.captureLayoutAnalyzedState();
          } else if (progress === StepperState.DESIGN_SYSTEM_MAPPED) {
            this.logger.info('Design System Mapped state detected');

            // Capture the design system mapped state permanently in artifacts
            this.captureDesignSystemMappedState();
          } else if (progress === StepperState.COMPLETED) {
            this.logger.info('Process completed state detected');

            // Clear any existing toasts before showing a new one
            this.toastService.clear();

            // Show toast notification for process completion only if we're not navigating away
            if (this.router.url.includes('/experience/generate')) {
              this.toastService.success('Code generation completed successfully.');
            }

            // Switch to preview view for the final result
            if (this.currentView$.value !== 'preview' && !this.userSelectedTab) {
              this.logger.info(
                'Auto-switching to preview tab for completed process (user has not manually selected a tab)'
              );
              this.togglePreviewView();
              // Reset the flag since this was an automatic switch, not user-initiated
              this.userSelectedTab = false;
            }

            // Enable tabs based on the state
            if (!this.previewError$.value) {
              // Enable all tabs when the process is complete without errors
              this.isCodeTabEnabled = true;
              this.isCodeGenerationComplete = true;
              this.logger.info('Process completed successfully, enabling all tabs');
            } else {
              // Only enable preview and logs tabs when there are errors
              this.isCodeTabEnabled = false;
              this.logger.info('Process completed with errors, code tab remains disabled');
            }

            this.isPreviewTabEnabled = true;
            this.isLogsTabEnabled = true;
          }
        }
      })
    );

    // Subscribe to polling service progress description updates
    this.subscription.add(
      this.pollingService.progressDescription$.subscribe(description => {
        if (description && description.trim() !== '') {
          this.logger.info('Received progress description from polling service:', description);

          // Add the progress description to the chat window with a typewriter effect
          this.lastProgressDescription = description;
          // this.addProgressDescriptionToChat(description);

          // Check for specific progress states in the description and update artifacts accordingly
          if (description.includes('LAYOUT_ANALYZED')) {
            this.logger.info('LAYOUT_ANALYZED detected in progress description');
            // Set the flag that layout analyzed data is available
            this.hasLayoutAnalyzed = true;
            // Capture the layout analyzed state permanently in artifacts
            this.captureLayoutAnalyzedState();
          } else if (description.includes('DESIGN_SYSTEM_MAPPED')) {
            this.logger.info('DESIGN_SYSTEM_MAPPED detected in progress description');
            // Set the flag that design system data is available
            this.hasDesignSystem = true;
            // Capture the design system mapped state permanently in artifacts
            this.captureDesignSystemMappedState();
          }

          // Try to parse layout data from the description
          if (description.includes('{') && description.includes('}')) {
            try {
              const jsonStartIndex = description.indexOf('{');
              const jsonEndIndex = description.lastIndexOf('}') + 1;

              if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
                const jsonPart = description.substring(jsonStartIndex, jsonEndIndex);
                const parsedData = JSON.parse(jsonPart);

                // Check if it contains layout data
                if (parsedData && typeof parsedData === 'object') {
                  // Look for data property that might contain layout keys
                  const layoutKeys = parsedData.data;
                  this.logger.debug(
                    'Layout keys from description data field:',
                    layoutKeys,
                    'Type:',
                    typeof layoutKeys
                  );

                  if (layoutKeys) {
                    // Reset layout data
                    this.layoutData = [];

                    // Process layout keys based on their type
                    if (typeof layoutKeys === 'string') {
                      // If it's a single layout key as a string
                      if (this.layoutMapping[layoutKeys]) {
                        this.layoutData = [layoutKeys];
                        this.logger.debug(
                          'Using single valid layout key:',
                          layoutKeys,
                          'Display name:',
                          this.layoutMapping[layoutKeys]
                        );
                      } else {
                        // If the key is not valid, clear the layout data to trigger loading state
                        this.layoutData = [];
                        this.logger.debug('Invalid layout key in string data:', layoutKeys);
                      }
                    } else if (Array.isArray(layoutKeys)) {
                      // Filter only valid layout keys that exist in our mapping
                      const validKeys = layoutKeys.filter(
                        key => typeof key === 'string' && this.layoutMapping[key]
                      );
                      // Use all valid keys
                      if (validKeys.length > 0) {
                        this.layoutData = validKeys;
                        this.logger.debug(`Using ${validKeys.length} valid layout keys:`, validKeys);
                        validKeys.forEach(key => {
                          this.logger.debug(`- ${key}: ${this.layoutMapping[key]}`);
                        });
                      } else {
                        // If no valid keys found, clear the layout data to trigger loading state
                        this.layoutData = [];
                        this.logger.debug('No valid layout keys found in array data');
                      }
                    } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {
                      // Extract keys from the object
                      const validKeys = Object.keys(layoutKeys).filter(
                        key => this.layoutMapping[key]
                      );
                      // Use all valid keys
                      if (validKeys.length > 0) {
                        this.layoutData = validKeys;
                        this.logger.debug(
                          `Using ${validKeys.length} valid layout keys from object:`,
                          validKeys
                        );
                        validKeys.forEach(key => {
                          this.logger.debug(`- ${key}: ${this.layoutMapping[key]}`);
                        });
                      } else {
                        // If no valid keys found, clear the layout data to trigger loading state
                        this.layoutData = [];
                        this.logger.debug('No valid layout keys found in object data');
                      }
                    }

                    this.logger.debug('Extracted layout key from description:', this.layoutData);

                    // If no layouts were detected, keep the array empty (don't use default fallback)
                    if (this.layoutData.length === 0) {
                      this.logger.debug(
                        'No valid layout keys detected in progress description, keeping empty array'
                      );
                    } else {
                      // If we have valid layout data, capture the state for artifacts based on the description
                      if (description.includes('LAYOUT_ANALYZED')) {
                        // Capture the layout analyzed state
                        this.captureLayoutAnalyzedState();
                        this.logger.info('Captured layout analyzed state from progress description');
                      } else if (description.includes('DESIGN_SYSTEM_MAPPED')) {
                        // Store the design system data for later use
                        this.designSystemData = parsedData.data;
                        // Capture the design system mapped state
                        this.captureDesignSystemMappedState();
                        this.logger.info('Captured design system mapped state from progress description');
                      }
                    }

                    // No need for loading delay - show immediately
                    this.isLayoutLoading = false;
                  }
                }
              }
            } catch (e) {
              this.logger.error('Error parsing layout data from description:', e);
            }
          }

          // Enable the logs tab since we have progress updates
          this.isLogsTabEnabled = true;
        }
      })
    );

    // Subscribe to polling service logs updates
    this.subscription.add(
      this.pollingService.logs$.subscribe(logs => {
        if (logs && logs.length > 0) {
          this.logger.info('Received logs from polling service:', logs.length, 'logs');

          // Process only new logs with animation effect
          this.startLogStreaming(logs);

          // Set hasLogs to true to enable the logs tab
          this.hasLogs = true;
          this.logger.info('Logs available, enabling logs tab');

          // We no longer need to set isLogsTabEnabled since we're using hasLogs to control tab clickability

          // Look for logs that might contain JSON data with layout information
          for (const log of logs) {
            if (typeof log === 'string' && log.includes('"message"') && log.includes('"data"')) {
              try {
                // Extract the JSON part from the log
                const jsonStartIndex = log.indexOf('{');
                if (jsonStartIndex !== -1) {
                  const jsonPart = log.substring(jsonStartIndex);
                  const parsedData = JSON.parse(jsonPart);

                  // Check if it has the expected structure
                  if (parsedData.message && parsedData.data) {
                    this.logger.info('Found layout data in logs:', parsedData);

                    // Directly extract the layout keys from the data field
                    const layoutKeys = parsedData.data;
                    this.logger.info(
                      'Layout keys from data field:',
                      layoutKeys,
                      'Type:',
                      typeof layoutKeys
                    );

                    // Reset layout data
                    this.layoutData = [];

                    // Process layout keys based on their type
                    if (typeof layoutKeys === 'string') {
                      // If it's a single layout key as a string
                      if (this.layoutMapping[layoutKeys]) {
                        this.layoutData = [layoutKeys];
                      }
                    } else if (Array.isArray(layoutKeys)) {
                      // Filter only valid layout keys that exist in our mapping
                      const validKeys = layoutKeys.filter(
                        key => typeof key === 'string' && this.layoutMapping[key]
                      );
                      // Only use the first valid key
                      if (validKeys.length > 0) {
                        this.layoutData = [validKeys[0]];
                      }
                    } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {
                      // Extract keys from the object
                      const validKeys = Object.keys(layoutKeys).filter(
                        key => this.layoutMapping[key]
                      );
                      // Only use the first valid key
                      if (validKeys.length > 0) {
                        this.layoutData = [validKeys[0]];
                      }
                    }

                    this.logger.info('Extracted layout key from logs:', this.layoutData);

                    // If no layouts were detected, keep the array empty (don't use default fallback)
                    if (this.layoutData.length === 0) {
                      this.logger.info('No valid layout keys detected in logs, keeping empty array');
                    } else {
                      // If we have valid layout data, capture the state for artifacts based on the log content
                      if (log.includes('LAYOUT_ANALYZED')) {
                        // Capture the layout analyzed state
                        this.captureLayoutAnalyzedState();
                        this.logger.info('Captured layout analyzed state from logs');
                      } else if (log.includes('DESIGN_SYSTEM_MAPPED')) {
                        // Store the design system data for later use
                        this.designSystemData = parsedData.data;
                        // Capture the design system mapped state
                        this.captureDesignSystemMappedState();
                        this.logger.info('Captured design system mapped state from logs');
                      }
                    }

                    // No need for loading delay - show immediately
                    this.isLayoutLoading = false;
                  }
                }
              } catch (e) {
                this.logger.error('Error parsing JSON from log:', e);
              }
            }
          }

          // We don't add logs to the chat window anymore
          // Only handle critical errors in the chat
          this.addLogUpdateToChatWindow(logs);
        }
      })
    );

    // Subscribe to theme changes
    this.themeService.themeObservable
      .pipe(takeUntil(this.destroy$))
      .subscribe((theme: 'light' | 'dark') => {
        this.currentTheme$.next(theme);
        // Update icons based on the new theme
      });

    // Get the generated code from the service
    this.generatedCode = this.codeSharingService.getGeneratedCode();
    this.logger.info('Generated code from service:', this.generatedCode);

    // If we already have code, process it immediately
    if (this.generatedCode) {
      this.logger.info('\u2705 Code already available in code-sharing service');
      this.isCodeGenerationComplete = true; // Set the flag to indicate code generation is complete
      this.isPreviewLoading$.next(false); // Ensure preview loading is disabled

      // Empty the layoutData array
      this.layoutData = [];
      this.logger.info('Code already available, emptied layoutData array:', this.layoutData);

      // Set to preview mode only if user hasn't manually selected a tab
      if (!this.userSelectedTab) {
        this.currentView$.next('preview');
      } else {
        this.logger.info('Respecting user tab selection, not switching to preview');
      }

      // Force check if iframe exists after a short delay
      setTimeout(() => {
        const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
        this.logger.debug('Iframe check for existing code:', !!iframe);
        if (!iframe) {
          this.logger.warn('Iframe not found in DOM for existing code, forcing reload of preview view');
        }
      }, 200);

      this.processCodeFromService(this.generatedCode);
    } else {
      // Check if we need to start polling
      if (this.projectId && this.jobId) {
        this.logger.info('\u2705 Starting polling service from code-window component');
        this.pollingService.startPolling(this.projectId, this.jobId, {
          taskType: 'code generation',
          // Using fixed 5-second polling interval
        });
        this.updatePollingStatus(true);

        // Generate the app name for the iframe URL if not already done
        if (!this.appName) {
          this.generateAndSetAppName();
        }
      }
    }

    // Subscribe to future code updates
    this.subscription = this.codeSharingService.generatedCode$.subscribe(code => {
      if (code) {
        this.logger.info('\u2705 Received new generated code from service');
        this.isCodeGenerationComplete = true; // Set the flag to indicate code generation is complete
        this.processCodeFromService(code);
      }
    });
  }

  /**
   * Process API response for layout data
   * @param response The API response containing progress information
   */
  processApiResponse(response: any): void {
    if (!response || !response.details) return;

    const { progress, progress_description, log } = response.details;

    // Format the progress description before processing
    let formattedDescription = progress_description;
    if (progress_description) {
      // Format LAYOUT_ANALYZED section
      if (progress_description.includes('LAYOUT_ANALYZED')) {
        formattedDescription = this.formatLayoutAnalyzedDescription(progress_description);
      }

      // Add the formatted description to the chat
      if (formattedDescription !== progress_description) {
        // this.addProgressDescriptionToChat(formattedDescription);
      } else {
        // this.addProgressDescriptionToChat(progress_description);
      }
    }

    // Update progress state
    if (progress) {
      this.currentProgressState = progress;

      // If it's a layout-related state, set loading state
      if (progress.includes('LAYOUT_ANALYZED') || progress.includes('DESIGN_SYSTEM_MAPPED')) {
        this.isLayoutLoading = true;

        // Process the log field which contains JSON string with layout data
        if (log && typeof log === 'string') {
          try {
            // Check if log is a JSON string
            if (log.includes('{') && log.includes('}')) {
              this.logger.info('Found potential JSON in log field:', log);

              // Try to parse the log as JSON
              const parsedLog = JSON.parse(log);

              // Check if it has the expected structure with message and data
              if (parsedLog.message && parsedLog.data) {
                this.logger.info('Found layout data in log field:', parsedLog);

                // Extract layout keys from the data field
                const layoutKeys = parsedLog.data;
                this.logger.info('Layout keys from log field:', layoutKeys, 'Type:', typeof layoutKeys);

                // Reset layout data
                this.layoutData = [];

                // Capture the state for artifacts based on the progress
                if (progress.includes('LAYOUT_ANALYZED')) {
                  // We'll capture the layout analyzed state after processing the data
                  this.logger.info('Will capture layout analyzed state after processing data');
                } else if (progress.includes('DESIGN_SYSTEM_MAPPED')) {
                  // Store the design system data for later use
                  this.designSystemData = parsedLog.data;
                  this.logger.info('Stored design system data:', this.designSystemData);
                }

                // Process layout keys based on their type
                if (typeof layoutKeys === 'string') {
                  // If it's a single layout key as a string
                  if (this.layoutMapping[layoutKeys]) {
                    this.layoutData = [layoutKeys];
                  }
                } else if (Array.isArray(layoutKeys)) {
                  // Filter only valid layout keys that exist in our mapping
                  const validKeys = layoutKeys.filter(
                    key => typeof key === 'string' && this.layoutMapping[key]
                  );
                  // Only use the first valid key
                  if (validKeys.length > 0) {
                    this.layoutData = [validKeys[0]];
                  }
                } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {
                  // Extract keys from the object
                  const validKeys = Object.keys(layoutKeys).filter(key => this.layoutMapping[key]);
                  // Only use the first valid key
                  if (validKeys.length > 0) {
                    this.layoutData = [validKeys[0]];
                  }
                }
              }
            }
          } catch (e) {
            this.logger.error('Error parsing JSON from log field:', e);
          }
        }

        // If we don't have layout data, keep it empty and show loading
        if (
          !this.layoutData ||
          this.layoutData.length === 0 ||
          !this.layoutMapping[this.layoutData[0]]
        ) {
          this.logger.info(
            'No layout data detected in API response, keeping empty array and showing loading'
          );
          // Don't use default fallback
          this.isLayoutLoading = true;
          this.isPreviewLoading$.next(true);
          this.isLoading$.next(true);
        } else {
          // If we have valid layout data, hide loading
          this.isLayoutLoading = false;
          this.isPreviewLoading$.next(false);
          this.isLoading$.next(false);

          // Capture the state for artifacts based on the progress
          if (progress.includes('LAYOUT_ANALYZED')) {
            // Now that we have processed the data, capture the layout analyzed state
            this.captureLayoutAnalyzedState();
            this.logger.info('Captured layout analyzed state after processing data');
          } else if (progress.includes('DESIGN_SYSTEM_MAPPED')) {
            // Now that we have processed the data, capture the design system mapped state
            this.captureDesignSystemMappedState();
            this.logger.info('Captured design system mapped state after processing data');
          }
        }
      }
    }

    // Process progress description for layout data as fallback
    if (progress_description && (!this.layoutData || this.layoutData.length === 0)) {
      // Look for JSON data in the description
      if (progress_description.includes('{') && progress_description.includes('}')) {
        try {
          const jsonStartIndex = progress_description.indexOf('{');
          const jsonEndIndex = progress_description.lastIndexOf('}') + 1;

          if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
            const jsonPart = progress_description.substring(jsonStartIndex, jsonEndIndex);
            const parsedData = JSON.parse(jsonPart);

            // Check if it has the expected structure with message and data
            if (parsedData.message && parsedData.data) {
              this.logger.info('Found layout data in progress description as fallback:', parsedData);

              // Extract layout keys from the data field
              const layoutKeys = parsedData.data;
              this.logger.info(
                'Layout keys from progress description:',
                layoutKeys,
                'Type:',
                typeof layoutKeys
              );

              // Reset layout data
              this.layoutData = [];

              // Process layout keys based on their type
              if (typeof layoutKeys === 'string') {
                // If it's a single layout key as a string
                if (this.layoutMapping[layoutKeys]) {
                  this.layoutData = [layoutKeys];
                }
              } else if (Array.isArray(layoutKeys)) {
                // If it's an array of layout keys
                this.layoutData = layoutKeys.filter(
                  key => typeof key === 'string' && this.layoutMapping[key]
                );

                // Log the image paths for each layout key
                if (this.layoutData.length > 0) {
                  this.layoutData.forEach(_key => {
                    // Placeholder for future implementation if needed
                  });
                }
              } else if (typeof layoutKeys === 'object' && layoutKeys !== null) {
                // If it's an object, extract the keys
                this.layoutData = Object.keys(layoutKeys).filter(key => this.layoutMapping[key]);

                // Log the image paths for each layout key
                if (this.layoutData.length > 0) {
                  this.layoutData.forEach(_key => {
                    // Placeholder for future implementation if needed
                  });

                  // If we have valid layout data, capture the state for artifacts based on the progress
                  if (progress_description.includes('LAYOUT_ANALYZED')) {
                    // Capture the layout analyzed state
                    this.captureLayoutAnalyzedState();
                    this.logger.info('Captured layout analyzed state from progress description fallback');
                  } else if (progress_description.includes('DESIGN_SYSTEM_MAPPED')) {
                    // Store the design system data for later use
                    this.designSystemData = parsedData.data;
                    // Capture the design system mapped state
                    this.captureDesignSystemMappedState();
                    this.logger.info('Captured design system mapped state from progress description fallback');
                  }
                }
              }
            }
          }
        } catch (e) {
          this.logger.error('Error parsing JSON from progress description:', e);
        }
      }

      // If still no layouts were detected, keep the array empty (don't use default fallback)
      if (!this.layoutData || this.layoutData.length === 0) {
        this.logger.info('No layout data detected anywhere, keeping empty array');
        // Don't use default fallback
      }
    }
  }

  /**
   * Get a page title based on index and layout key
   * @param index The index of the page
   * @param layoutKey The layout key
   * @returns A page title
   */
  getPageTitle(index: number, layoutKey: string): string {
    // Default page titles for all 8 possible pages
    const defaultTitles = [
      'Home Page',
      'About Us Page',
      'Products Page',
      'Services Page',
      'Blog Page',
      'Contact Page',
      'Gallery Page',
      'FAQ Page',
    ];

    // If we have a layout key, use it to create a more specific title
    if (layoutKey && this.layoutMapping[layoutKey]) {
      // Create a title based on the default title and layout
      return defaultTitles[index % defaultTitles.length] + ` (${layoutKey})`;
    }

    // Return default title if available, otherwise generate a generic one
    return defaultTitles[index % defaultTitles.length] || `Page ${index + 1}`;
  }

  // Process code received from the code-sharing service
  private processCodeFromService(code: any): void {
    this.logger.info('Processing code from service');
    this.generatedCode = code;

    // Convert code to file models
    const fileModels: FileModel[] = [];

    // Handle different formats of code
    if (Array.isArray(this.generatedCode)) {
      this.logger.info('Processing array format code with', this.generatedCode.length, 'files');

      // Check if it's the new format with fileName and content properties
      if (
        this.generatedCode.length > 0 &&
        'fileName' in this.generatedCode[0] &&
        'content' in this.generatedCode[0]
      ) {
        // This is the format from the polling service with fileName and content properties
        for (const file of this.generatedCode) {
          fileModels.push({
            name: file.fileName,
            type: 'file',
            content:
              typeof file.content === 'string'
                ? file.content
                : JSON.stringify(file.content, null, 2),
          });
        }
        this.logger.info(
          'Processed array of files with fileName/content format, count:',
          fileModels.length
        );
      } else {
        // Handle other array formats
        for (const item of this.generatedCode) {
          if (typeof item === 'object' && item !== null) {
            // Try to extract file information
            const name = item.name || item.fileName || item.path || 'unknown.txt';
            const content = item.content || '';
            fileModels.push({
              name,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            });
          }
        }
        this.logger.info('Processed generic array format, file count:', fileModels.length);
      }
    } else if (typeof this.generatedCode === 'string') {
      // Try to parse string as JSON
      try {
        const parsedCode = JSON.parse(this.generatedCode);
        this.logger.info('Parsed string code as JSON:', typeof parsedCode);

        if (Array.isArray(parsedCode)) {
          // Handle array of files
          for (const item of parsedCode) {
            if (typeof item === 'object' && item !== null) {
              const name = item.fileName || item.name || item.path || 'unknown.txt';
              const content = item.content || '';
              fileModels.push({
                name,
                type: 'file',
                content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
              });
            }
          }
        } else if (typeof parsedCode === 'object') {
          // Handle object with file paths as keys
          for (const [filePath, content] of Object.entries(parsedCode)) {
            fileModels.push({
              name: filePath,
              type: 'file',
              content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
            });
          }
        }
      } catch (e) {
        this.logger.warn('Generated code is not valid JSON:', e);
        // Use the string as a single file content
        fileModels.push({
          name: 'output.txt',
          type: 'file',
          content: this.generatedCode,
        });
      }
    } else if (typeof this.generatedCode === 'object' && this.generatedCode !== null) {
      // Handle object format (key-value pairs where key is file path and value is content)
      for (const [filePath, content] of Object.entries(this.generatedCode)) {
        fileModels.push({
          name: filePath,
          type: 'file',
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
        });
      }
      this.logger.info('Processed object format, file count:', fileModels.length);
    } else {
      this.logger.warn('Generated code is not in expected format:', typeof this.generatedCode);
    }

    // Get the selected technology from the app state
    const projectState = this.appStateService.getProjectState();
    const technology = projectState.technology || 'angular';
    this.logger.info('Project state:', projectState);
    this.logger.info('Ordering files based on selected technology:', technology);

    // Sort the files based on the selected technology
    const sortedFileModels = this.sortFilesByTechnology(fileModels, technology);

    // Log the first few files before and after sorting
    this.logger.info(
      'First 5 files before sorting:',
      fileModels.slice(0, 5).map(f => f.name)
    );
    this.logger.info(
      'First 5 files after sorting:',
      sortedFileModels.slice(0, 5).map(f => f.name)
    );

    // Update the files property if we have any files using BehaviorSubject
    if (sortedFileModels.length > 0) {
      this.files$.next(sortedFileModels);
      this.logger.info('Updated files property with sorted code, file count:', sortedFileModels.length);
    } else {
      this.logger.warn('No files were extracted from the generated code');
    }

    // Update flags when code is received using BehaviorSubjects
    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false); // Ensure preview loading is also disabled
    this.isCodeGenerationComplete = true; // Set the flag to indicate code generation is complete

    // Empty the layoutData array when code is received
    this.layoutData = [];
    this.logger.info('Code received, emptied layoutData array:', this.layoutData);

    // Switch to preview view only if user hasn't manually selected a tab
    if (!this.userSelectedTab) {
      this.logger.info(
        'Auto-switching to preview view after code processing (user has not manually selected a tab)'
      );
      this.togglePreviewView();
    } else {
      this.logger.info(
        'Respecting user tab selection, not auto-switching to preview after code processing'
      );
      // Still need to update the tab states without changing the view using BehaviorSubjects
      this.isCodeActive$.next(false);
      this.isPreviewActive$.next(true);
      this.isLogsActive$.next(false);
    }

    // Force check if iframe exists after a short delay
    setTimeout(() => {
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      this.logger.debug('Iframe check in processCodeFromService:', !!iframe);
      if (!iframe) {
        this.logger.warn(
          'Iframe not found in DOM after code processing, forcing reload of preview view'
        );
        // Force a refresh of the view only if we're in preview view
        if (this.currentView$.value === 'preview') {
          this.logger.info('Forcing refresh of preview view');
          this.currentView$.next('editor');
          setTimeout(() => {
            this.currentView$.next('preview');
          }, 50);
        } else {
          this.logger.info('Not forcing refresh since we are not in preview view');
        }
      }
    }, 200);

    // If we have a deployed URL from the service, use it; otherwise, use our hardcoded URL
    if (this.codeSharingService.getDeployedUrl()) {
      this.deployedUrl$.next(this.codeSharingService.getDeployedUrl() ?? '');
    } else if (!this.deployedUrl$.value) {
      // Ensure we have a URL - use the hardcoded one if nothing else is available
      this.deployedUrl$.next('https://orange-island-09111c10f.6.azurestaticapps.net/');
    }

    // Always enable the preview tab since we have a URL
    this.isPreviewTabEnabled = true;

    // Always sanitize the URL to ensure it works in the iframe
    if (this.deployedUrl$.value) {
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
      this.logger.info('URL sanitized in processCodeFromService:', this.deployedUrl$.value);
    } else {
      this.urlSafe = '';
    }

    // The generatedCode should now come from the AppStateService via the CodeSharingService
    // No need to manually check sessionStorage

    // Subscribe to deployed URL changes
    this.subscription?.add(
      this.codeSharingService.deployedUrl$.subscribe(url => {
        this.deployedUrl$.next(url ?? '');
        if (url) {
          this.isPreviewLoading$.next(false);
          this.previewIcon$.next('bi-eye');
          this.isPreviewTabEnabled = true; // Enable the preview tab
        }
      })
    );
  }

  toggleView() {
    if (!this.deployedUrl$.value && this.currentView$.value === 'editor') {
      // Start preview generation
      this.isPreviewLoading$.next(true);
      this.previewIcon$.next('bi-arrow-clockwise');
      this.previewError$.next(false);

      const generatedCode = this.codeSharingService.getGeneratedCode();
      if (generatedCode) {
        this.codeGenerationService.getPreviewDeployment(generatedCode).subscribe({
          next: response => {
            if (response.status === 'success' && response.deployedUrl) {
              this.codeSharingService.setDeployedUrl(response.deployedUrl);
              this.currentView$.next('preview');
              this.previewError$.next(false);
            } else {
              this.previewError$.next(true);
              this.currentView$.next('preview');
            }
          },
          error: error => {
            this.logger.error('Preview deployment failed:', error);
            this.isPreviewLoading$.next(false);
            this.previewIcon$.next('bi-code-slash');
            this.previewError$.next(true);
            this.currentView$.next('preview');
          },
          complete: () => {
            this.isPreviewLoading$.next(false);
          },
        });
      }
    } else {
      // Toggle between existing views
      const newView = this.currentView$.value === 'editor' ? 'preview' : 'editor';
      this.currentView$.next(newView);
      this.previewIcon$.next(newView === 'editor' ? 'bi-code-slash' : 'bi-eye');
    }
  }

  /**
   * Handle window resize events to ensure split screen remains responsive
   */
  @HostListener('window:resize')
  onWindowResize() {
    // Only update if not currently resizing to avoid conflicts
    if (!this.isResizing$.value) {
      // Get DOM elements
      const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
      const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
      const container = document.querySelector('.awe-splitscreen') as HTMLElement;

      if (leftPanel && rightPanel && container && !this.isLeftPanelCollapsed$.value) {
        // Ensure percentages are maintained during window resize
        const containerWidth = container.offsetWidth;

        // Parse the percentage values (remove the % sign)
        const leftWidthStr = this.defaultLeftPanelWidth;
        const rightWidthStr = this.defaultRightPanelWidth;

        // Extract numeric values from percentage strings
        const leftWidthPercent = parseFloat(leftWidthStr);
        const rightWidthPercent = parseFloat(rightWidthStr);

        // Validate that we have percentage values
        if (!isNaN(leftWidthPercent) && !isNaN(rightWidthPercent)) {
          // Calculate minimum width in pixels
          const minWidthPx = parseInt(this.minWidth$.value || '300', 10);

          // Check if left panel would be smaller than minimum width
          if ((containerWidth * leftWidthPercent / 100) < minWidthPx) {
            // Adjust to maintain minimum width
            const newLeftPercentage = (minWidthPx / containerWidth * 100).toFixed(2) + '%';
            const newRightPercentage = ((containerWidth - minWidthPx) / containerWidth * 100).toFixed(2) + '%';

            leftPanel.style.width = newLeftPercentage;
            rightPanel.style.width = newRightPercentage;

            // Update stored values
            this.defaultLeftPanelWidth = newLeftPercentage;
            this.defaultRightPanelWidth = newRightPercentage;
          }

          // Check if right panel would be smaller than minimum width
          else if ((containerWidth * rightWidthPercent / 100) < minWidthPx) {
            // Adjust to maintain minimum width
            const newRightPercentage = (minWidthPx / containerWidth * 100).toFixed(2) + '%';
            const newLeftPercentage = ((containerWidth - minWidthPx) / containerWidth * 100).toFixed(2) + '%';

            leftPanel.style.width = newLeftPercentage;
            rightPanel.style.width = newRightPercentage;

            // Update stored values
            this.defaultLeftPanelWidth = newLeftPercentage;
            this.defaultRightPanelWidth = newRightPercentage;
          }
        }
      }
    }
  }

  ngOnDestroy() {

    // 1. Unsubscribe from all subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }

    // 2. Complete and clean up the destroy$ subject used with takeUntil
    this.destroy$.next();
    this.destroy$.complete();

    // 3. Clear all timers and intervals
    if (this.checkSessionStorageInterval) {
      clearInterval(this.checkSessionStorageInterval);
      this.checkSessionStorageInterval = null;
    }

    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
      this.logStreamTimer = null;
    }

    if (this.autoSwitchToLogsTimer) {
      clearTimeout(this.autoSwitchToLogsTimer);
      this.autoSwitchToLogsTimer = null;
    }
    // Always reset logs regardless of polling state
    this.pollingService.resetLogs();

    if (this.isPolling) {
      this.pollingService.stopPolling();
      this.isPolling = false;
    }
this.currentProgressState = '';
    this.lastProgressDescription = '';
    this.pollingStatus = 'PENDING';
    this.hasLogs = false;
    this.logMessages = [];
    this.formattedLogMessages = [];
    this.processedLogHashes.clear();
    this.expandedCodeLogs.clear();
    this.promptService.setImage(null);
    this.promptService.setPrompt('');
    this.promptSubmissionService.resetSubmissionState();

    this.codeSharingService.resetState();

    this.appStateService.updateProjectState({
      codeGenerated: false,
      generatedCode: null
    });

    this.resetComponentState();

    // 7. Clean up element selection mode if active
    if (this.isElementSelectionMode) {
      this.cleanupSelectionMode();
      this.isElementSelectionMode = false;
      this.clearSelectedElement();
    }

    // 8. Clear any references to DOM elements and clean up iframe
    try {
      // Clean up iframe if it exists
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (iframe && iframe.contentWindow) {
        // Try to clean up iframe content
        iframe.src = 'about:blank';
      }
    } catch (error) {
      this.logger.error('Error cleaning up iframe:', error);
    }

    // Set URL safe to null to release any references
    this.urlSafe = undefined;

    // Reset the user selected tab flag
    this.userSelectedTab = false;


    // Clear the current project ID from the stepper state service
    this.stepperStateService.clearCurrentProjectId();

    // Remove any lingering event listeners or classes
    document.body.classList.remove('user-select-none');

    // Clean up any active resize operation
    if (this.isResizing$.value) {
      const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
      const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
      const resizer = document.querySelector('.resizer') as HTMLElement;

      if (leftPanel) leftPanel.classList.remove('dragging');
      if (rightPanel) rightPanel.classList.remove('dragging');
      if (resizer) resizer.classList.remove('active');

      this.isResizing$.next(false);
    }

    this.destroy$.next();
    this.destroy$.complete();
  }

  // Update polling status in the UI
  private updatePollingStatus(isPolling: boolean): void {
    this.isPolling = isPolling;
    if (isPolling) {
      // Only show loading view initially, but don't force it if user has switched to another view
      if (this.currentView$.value === 'loading') {
        this.isLoading$.next(true);
      }

      // Show toast notification when polling starts
      this.toastService.info('Starting code generation process...');

      // Clear the selected image data URI when code generation starts
      // This ensures the image is only shown for the first prompt
      if (this.selectedImageDataUri) {
        this.selectedImageDataUri = '';
      }
    } else {
    }
  }

  /**
   * Starts the resize operation for the split screen
   * Implements smooth dragging with text selection prevention
   * @param event The mouse event that triggered the resize
   */
  startResize(event: MouseEvent) {
    // Prevent default to avoid text selection
    event.preventDefault();

    // Set resizing flag
    this.isResizing$.next(true);

    // Get DOM elements
    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const resizer = document.querySelector('.resizer') as HTMLElement;
    const container = document.querySelector('.awe-splitscreen') as HTMLElement;
    const splitScreen = document.querySelector('.smooth-split-screen') as HTMLElement;

    if (!leftPanel || !rightPanel || !container || !resizer) {
      this.logger.error('Required DOM elements not found for resize operation');
      return;
    }

    // Add active class to resizer for visual feedback
    resizer.classList.add('active');

    // Add dragging class to panels to disable transitions during drag
    leftPanel.classList.add('dragging');
    rightPanel.classList.add('dragging');

    // Add resizing class to split screen to prevent text selection
    if (splitScreen) {
      splitScreen.classList.add('resizing');
    }

    // Add user-select-none class to body to prevent text selection during resize
    document.body.classList.add('user-select-none');

    // Add resizing class to html element for global text selection prevention
    document.documentElement.classList.add('resizing-active');

    // Store initial positions
    const initialX = event.clientX;
    const containerWidth = container.offsetWidth;
    const initialLeftWidth = leftPanel.offsetWidth;

    // Calculate min widths (use minWidth from component or default to 300px)
    const minWidth = parseInt(this.minWidth$.value || '300', 10);
    const maxLeftWidth = containerWidth - minWidth;
    const minLeftWidth = minWidth;

    // Track velocity for momentum effect
    let lastX = initialX;
    let velocity = 0;
    let lastUpdateTime = Date.now();

    // Track animation frame for smoother performance
    let animationFrameId: number | null = null;

    // Create mousemove handler with requestAnimationFrame for smoother performance
    const handleMouseMove = (e: MouseEvent) => {
      // Prevent default and stop propagation to avoid text selection
      e.preventDefault();
      e.stopPropagation();

      // Cancel any pending animation frame
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
      }

      // Use requestAnimationFrame for smoother updates
      animationFrameId = requestAnimationFrame(() => {
        // Calculate how far the mouse has moved
        const dx = e.clientX - initialX;

        // Calculate new width as a percentage of container
        let newLeftWidth = initialLeftWidth + dx;

        // Apply constraints
        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        // Calculate percentage values
        const leftPercentage = (newLeftWidth / containerWidth * 100).toFixed(2) + '%';
        const rightPercentage = ((containerWidth - newLeftWidth) / containerWidth * 100).toFixed(2) + '%';

        // Apply new widths with hardware acceleration
        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        // Store the new widths as defaults for when panel is toggled
        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;

        // Calculate velocity for momentum effect
        const now = Date.now();
        const elapsed = now - lastUpdateTime;
        if (elapsed > 0) {
          velocity = (e.clientX - lastX) / elapsed;
          lastX = e.clientX;
          lastUpdateTime = now;
        }

        // Force change detection to update the UI
        this.ngZone.run(() => {
          this.cdr.detectChanges();
        });

        // Reset animation frame ID
        animationFrameId = null;
      });
    };

    // Create mouseup handler
    const handleMouseUp = () => {
      // Cancel any pending animation frame
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }

      // Remove event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove, { passive: false } as EventListenerOptions);
      document.removeEventListener('touchend', handleTouchEnd);

      // Apply momentum effect if velocity is significant
      if (Math.abs(velocity) > 0.5) {
        const momentum = velocity * 10; // Adjust this multiplier for momentum strength
        let newLeftWidth = leftPanel.offsetWidth + momentum;

        // Apply constraints
        newLeftWidth = Math.max(minLeftWidth, Math.min(maxLeftWidth, newLeftWidth));

        // Calculate percentage values
        const leftPercentage = (newLeftWidth / containerWidth * 100).toFixed(2) + '%';
        const rightPercentage = ((containerWidth - newLeftWidth) / containerWidth * 100).toFixed(2) + '%';

        // Apply momentum with smooth transition
        leftPanel.classList.remove('dragging');
        rightPanel.classList.remove('dragging');

        // Apply new widths
        leftPanel.style.width = leftPercentage;
        rightPanel.style.width = rightPercentage;

        // Store the new widths
        this.defaultLeftPanelWidth = leftPercentage;
        this.defaultRightPanelWidth = rightPercentage;
      } else {
        // Remove dragging class to re-enable transitions
        leftPanel.classList.remove('dragging');
        rightPanel.classList.remove('dragging');
      }

      // Reset state
      this.isResizing$.next(false);

      // Remove active class from resizer
      resizer.classList.remove('active');

      // Remove resizing class from split screen
      if (splitScreen) {
        splitScreen.classList.remove('resizing');
      }

      // Remove user-select-none class from body
      document.body.classList.remove('user-select-none');

      // Remove resizing class from html element
      document.documentElement.classList.remove('resizing-active');

      // Force change detection to update the UI
      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });
    };

    // Touch support for mobile devices with improved performance
    const handleTouchMove = (e: TouchEvent) => {
      // Prevent scrolling during resize
      e.preventDefault();

      if (e.touches.length > 0) {
        const touchEvent = e.touches[0];

        // Create a synthetic mouse event
        const mouseEvent = {
          clientX: touchEvent.clientX,
          preventDefault: () => e.preventDefault()
        } as MouseEvent;

        // Use the same animation frame-based handler for touch events
        handleMouseMove(mouseEvent);
      }
    };

    const handleTouchEnd = () => {
      handleMouseUp();
    };

    // Add event listeners with proper options for performance
    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchmove', handleTouchMove, { passive: false } as EventListenerOptions);
    document.addEventListener('touchend', handleTouchEnd);
  }
  onPanelToggled(isCollapsed: boolean) {
    this.isPanelCollapsed$.next(isCollapsed);
  }

  onFileChanged(_files: { filename: string; filecontent: string }[]) {
    // Placeholder for future implementation
  }

  onMessageReceived(_message: { type: string; data: any }) {
    // Placeholder for future implementation
  }

  onEditorReady(_vm: any) {
    this.editorReady = true;
    this.updateEditorWithGeneratedCode();
  }

  private updateEditorWithGeneratedCode() {
    try {

    } catch (error) {

    }
  }

  toggleHistoryView(): void {
    this.isHistoryActive$.next(!this.isHistoryActive$.value);
  }

  toggleCodeView(): void {
    // Only allow switching to code view if code is generated and there's no error
    if (!this.isCodeGenerationComplete) {
      this.logger.warn('Cannot switch to code view - code not yet generated');
      return;
    }

    // Don't allow switching to code view if there's an error
    if (this.previewError$.value) {
      this.logger.warn('Cannot switch to code view - preview has an error');
      return;
    }

    this.logger.info('Switching to code view');

    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states using BehaviorSubjects
    this.isCodeActive$.next(true);
    this.isPreviewActive$.next(false);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(false);

    // Set the current view to editor
    this.currentView$.next('editor');

    // Update UI state using BehaviorSubjects
    this.isLoading$.next(false);
    this.previewIcon$.next('bi-code-slash');

    // View will update automatically via reactive patterns
  }
  togglePreviewView(): void {
    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(false);

    // Set the current view to preview
    this.currentView$.next('preview');

    // View will update automatically via reactive patterns

    // Use the generated app name URL if available, otherwise use the hardcoded URL
    if (this.appName) {
      // Create the Azure website URL using the generated app name
      const azureUrl = `https://${this.appName}.azurewebsites.net`;
      this.deployedUrl$.next(azureUrl);
    } else if (this.projectId && this.jobId) {
      // If we have project ID and job ID but no app name yet, generate it
      this.generateAndSetAppName().then(() => {
        // URL will be updated in the generateAndSetAppName method
      });
    } else {
      // Fallback to hardcoded URL if no app name and no IDs available
      this.deployedUrl$.next('https://orange-island-09111c10f.6.azurestaticapps.net/');
    }

    // Always re-sanitize the URL to ensure it's properly handled
    if (this.deployedUrl$.value) {
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.deployedUrl$.value);
    } else {
      // If deployedUrl is null, use an empty string
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl('');

    }

    // Always hide loading spinner when code generation is complete and there's no error
    if (this.isCodeGenerationComplete && !this.previewError$.value) {
      this.isPreviewLoading$.next(false);

      // Force check if iframe exists
      setTimeout(() => {
        const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
        this.logger.debug('Iframe check:', !!iframe);
        if (!iframe) {
          this.logger.warn('Iframe not found in DOM, forcing reload of preview view');
          // Force a refresh of the view only if we're in preview view
          if (this.currentView$.value === 'preview') {
            this.logger.info('Forcing refresh of preview view');
            // Temporarily switch to another view and back to force iframe recreation
            const currentView = this.currentView$.value;
            this.currentView$.next('editor');
            setTimeout(() => {
              this.currentView$.next(currentView);
            }, 50);
          } else {
            this.logger.info('Not forcing refresh since we are not in preview view');
          }
        }
      }, 100);
    }
    // Hide loading spinner when we have a layout analyzed state AND valid layout data
    else if (
      this.currentProgressState &&
      (this.currentProgressState.includes('LAYOUT_ANALYZED') ||
        this.currentProgressState.includes('PAGES_GENERATED')) &&
      this.layoutData &&
      this.layoutData.length > 0 &&
      this.layoutMapping[this.layoutData[0]]
    ) {
      this.isPreviewLoading$.next(false);

      // Ensure layout data is available, contains only one key, and the key is valid
      if (
        this.currentProgressState.includes('LAYOUT_ANALYZED') ||
        this.currentProgressState.includes('PAGES_GENERATED')
      ) {
        // Only process layout data if we're not in the completed state
        if (!this.isCodeGenerationComplete) {
          if (!this.layoutData || this.layoutData.length === 0) {
          } else if (this.layoutData.length > 1) {
            // Ensure we only use the first layout key
            // Also verify the key is valid
            if (this.layoutMapping[this.layoutData[0]]) {
              this.layoutData = [this.layoutData[0]];
            } else {
              // If the first key is not valid, try to find a valid one
              const validKeys = this.layoutData.filter(key => this.layoutMapping[key]);
              if (validKeys.length > 0) {
                this.layoutData = [validKeys[0]];
              } else {
                // If no valid keys, don't use default fallback
                this.layoutData = [];
              }
            }
          } else {
            // We have exactly one key, but verify it's valid
            if (!this.layoutMapping[this.layoutData[0]]) {
              // If the key is not valid, don't use default fallback
              this.layoutData = [];
            }
          }
        } else {
          // If we're in the completed state, ensure layoutData is empty
          if (this.layoutData.length > 0) {
            this.layoutData = [];
          }
        }
      }

      // Make sure loading is disabled immediately
      this.isLayoutLoading = false;
    } else {
      // Show loading until we reach LAYOUT_ANALYZED state
      this.isPreviewLoading$.next(true);
    }
    this.previewIcon$.next('bi-eye');

    // Always enable the preview tab
    this.isPreviewTabEnabled = true;
  }

  toggleLogsView(): void {

    // Only proceed if logs are available
    if (!this.hasLogs) {
      return;
    }

    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states - keep the current view but set logs active flag using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isLogsActive$.next(true);
    this.isArtifactsActive$.next(false);

    // Set the current view to logs
    this.currentView$.next('logs');

    // Don't show loading spinner, we'll show loading animation in the view if no logs
    this.isLoading$.next(false);

    // Start streaming logs if we have any
    if (this.logMessages.length > 0) {
      this.startLogStreaming(this.logMessages);
    }

    // View will update automatically via reactive patterns
  }

  toggleArtifactsView(): void {
    // Only proceed if artifacts tab is enabled
    if (!this.isArtifactsTabEnabled) {
      this.toastService.info('Artifacts tab will be enabled after project overview is complete');
      return;
    }

    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Update tab states using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isLogsActive$.next(false);
    this.isArtifactsActive$.next(true);

    // Set the current view to artifacts
    this.currentView$.next('artifacts');

    // Debug: Log the current state of artifacts data and layout data
    this.logger.info('Artifacts data:', this.artifactsData);
    this.logger.info('Layout data:', this.layoutData);
    this.logger.info('Layout analyzed data:', this.layoutAnalyzedData);
    this.logger.info('Has layout analyzed:', this.hasLayoutAnalyzed);
    this.logger.info('Has design system:', this.hasDesignSystem);

    // Check if we need to update the artifacts based on the current state
    // Only add artifacts if they're not already there
    if (this.hasLayoutAnalyzed) {
      // Check if Layout Analyzed is already in artifacts
      const hasLayoutAnalyzedFile = this.artifactsData.some(file => file.name === 'Layout Analyzed');
      if (!hasLayoutAnalyzedFile) {
        // Add Layout Analyzed to artifacts
        this.ensureLayoutAnalyzedFileExists();
      }
    }

    if (this.hasDesignSystem) {
      // Check if Design System is already in artifacts
      const hasDesignSystemFile = this.artifactsData.some(file => file.name === 'Design System');
      if (!hasDesignSystemFile) {
        // Add Design System to artifacts
        this.captureDesignSystemMappedState();
      }
    }

    // If no artifact file is selected, select the first one
    if (!this.selectedArtifactFile && this.artifactsData.length > 0) {
      this.selectArtifactFile(this.artifactsData[0]);
    }

    // Force change detection to ensure view updates
    this.cdr.detectChanges();
  }

  /**
   * Selects an artifact file to display its content
   * @param file The artifact file to select
   */
  selectArtifactFile(file: any): void {
    this.logger.info('Selecting artifact file:', file);
    this.selectedArtifactFile = file;

    // If the selected file is the Design System, initialize the design tokens
    if (file && file.name === 'Design System' && file.type === 'component') {
      this.initializeDesignTokens();
    }

    // If the selected file is Layout Analyzed, make sure we have layout data
    if (file && file.name === 'Layout Analyzed' && file.type === 'image') {
      this.logger.info('Selected Layout Analyzed file, current layout data:', this.layoutData);
    }

    // Start typewriter animation for markdown content
    if (file && file.type === 'markdown') {
      this.startArtifactTypewriter(file);
    }

    this.cdr.detectChanges();
  }

  /**
   * Initialize layout analyzed data from the layout data
   * This method creates layout data objects for display in the artifacts tab
   */
  private initializeLayoutAnalyzedData(): void {
    // Clear existing layout analyzed data
    this.layoutAnalyzedData = [];

    // If we have layout data, use it to create layout analyzed data
    if (this.layoutData && this.layoutData.length > 0) {
      // Filter out any invalid layout keys
      const validLayoutKeys = this.layoutData.filter(key => this.layoutMapping[key]);

      if (validLayoutKeys.length > 0) {
        // Create layout analyzed data objects for each valid layout key
        this.layoutAnalyzedData = validLayoutKeys.map(key => ({
          key,
          name: this.layoutMapping[key] || 'Identified Layout',
          imageUrl: `assets/images/layout-${key}.png`
        }));

        this.logger.info('Initialized layout analyzed data with valid keys:', this.layoutAnalyzedData);
      } else {
        // If no valid layout keys, use a default layout
        this.layoutAnalyzedData = [{
          key: 'HB',
          name: 'Default Layout',
          imageUrl: 'assets/images/layout-HB.png'
        }];

        this.logger.info('No valid layout keys found, using default layout');
      }
    } else {
      // If no layout data is available, use a default layout
      this.layoutAnalyzedData = [{
        key: 'HB',
        name: 'Default Layout',
        imageUrl: 'assets/images/layout-HB.png'
      }];

      this.logger.info('No layout data available, using default layout');
    }

    // Always set the flag that layout analyzed data is available
    // This ensures the artifact is always shown once it's been detected
    if (this.hasLayoutAnalyzed) {
      // Ensure the Layout Analyzed file exists in artifacts data
      this.ensureLayoutAnalyzedFileExists();
    }

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  /**
   * Remove Layout Analyzed from artifacts data if it exists
   */
  private removeLayoutAnalyzedFromArtifacts(): void {
    const layoutAnalyzedIndex = this.artifactsData.findIndex(file => file.name === 'Layout Analyzed');
    if (layoutAnalyzedIndex !== -1) {
      this.artifactsData.splice(layoutAnalyzedIndex, 1);
      this.logger.info('Removed Layout Analyzed from artifacts data');

      // If the removed file was selected, select the first available file
      if (this.selectedArtifactFile && this.selectedArtifactFile.name === 'Layout Analyzed') {
        if (this.artifactsData.length > 0) {
          this.selectArtifactFile(this.artifactsData[0]);
        } else {
          this.selectedArtifactFile = null;
        }
      }
    }
  }

  /**
   * Ensure the Layout Analyzed file exists in the artifacts data
   * If it doesn't exist, add it with the appropriate image URL
   * If it does exist, update its content with the latest layout data
   */
  private ensureLayoutAnalyzedFileExists(): void {
    // Get the first layout image URL or use default
    let layoutImageUrl = 'assets/images/layout-HB.png'; // Default to HB layout
    if (this.layoutData && this.layoutData.length > 0) {
      const layoutKey = this.layoutData[0];
      layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    } else if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      layoutImageUrl = this.layoutAnalyzedData[0].imageUrl;
    }

    // Check if the Layout Analyzed file already exists
    const layoutAnalyzedIndex = this.artifactsData.findIndex(file => file.name === 'Layout Analyzed');

    if (layoutAnalyzedIndex === -1) {
      // If it doesn't exist, add it to artifacts data
      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl
      });

      this.logger.info('Added Layout Analyzed to artifacts data with image URL:', layoutImageUrl);
    } else {
      // If it exists, update its content with the latest layout data
      this.artifactsData[layoutAnalyzedIndex].content = layoutImageUrl;
      this.logger.info('Updated Layout Analyzed in artifacts data with image URL:', layoutImageUrl);
    }
  }

  /**
   * Capture the layout analyzed state from the polling response
   * This method ensures the layout analyzed data is permanently stored in artifacts
   */
  private captureLayoutAnalyzedState(): void {
    // Set the flag that layout analyzed data is available
    this.hasLayoutAnalyzed = true;

    // Initialize layout analyzed data
    this.initializeLayoutAnalyzedData();

    // Ensure the Layout Analyzed file exists in artifacts data
    // Get the first layout image URL or use default
    let layoutImageUrl = 'assets/images/layout-HB.png'; // Default to HB layout
    if (this.layoutData && this.layoutData.length > 0) {
      const layoutKey = this.layoutData[0];
      layoutImageUrl = `assets/images/layout-${layoutKey}.png`;
    } else if (this.layoutAnalyzedData && this.layoutAnalyzedData.length > 0) {
      layoutImageUrl = this.layoutAnalyzedData[0].imageUrl;
    }

    // Check if the Layout Analyzed file already exists
    const layoutAnalyzedIndex = this.artifactsData.findIndex(file => file.name === 'Layout Analyzed');

    if (layoutAnalyzedIndex === -1) {
      // If it doesn't exist, add it to artifacts data
      this.artifactsData.push({
        name: 'Layout Analyzed',
        type: 'image',
        content: layoutImageUrl
      });

      this.logger.info('Added Layout Analyzed to artifacts data with image URL:', layoutImageUrl);
    } else {
      // If it exists, update its content with the latest layout data
      this.artifactsData[layoutAnalyzedIndex].content = layoutImageUrl;
      this.logger.info('Updated Layout Analyzed in artifacts data with image URL:', layoutImageUrl);
    }

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  /**
   * Capture the design system mapped state from the polling response
   * This method ensures the design system data is permanently stored in artifacts
   */
  private captureDesignSystemMappedState(): void {
    // Set the flag that design system data is available
    this.hasDesignSystem = true;

    // Add Design System to artifacts data if not already present
    const hasDesignSystemFile = this.artifactsData.some(file => file.name === 'Design System');
    if (!hasDesignSystemFile) {
      // Create a placeholder for the design system component
      // The actual content will be rendered dynamically when the file is selected
      this.artifactsData.push({
        name: 'Design System',
        type: 'component',
        content: 'Design system information will be displayed here.'
      });

      // Initialize design tokens
      this.initializeDesignTokens();

      this.logger.info('Added Design System to artifacts data');

      // If this is the first artifact, select it automatically
      if (this.artifactsData.length === 1) {
        this.selectArtifactFile(this.artifactsData[0]);
      }
    }

    // View will update automatically via reactive patterns
  }

  /**
   * Remove Design System from artifacts data if it exists
   */
  private removeDesignSystemFromArtifacts(): void {
    const designSystemIndex = this.artifactsData.findIndex(file => file.name === 'Design System');
    if (designSystemIndex !== -1) {
      this.artifactsData.splice(designSystemIndex, 1);
      this.logger.info('Removed Design System from artifacts data');

      // If the removed file was selected, select the first available file
      if (this.selectedArtifactFile && this.selectedArtifactFile.name === 'Design System') {
        if (this.artifactsData.length > 0) {
          this.selectArtifactFile(this.artifactsData[0]);
        } else {
          this.selectedArtifactFile = null;
        }
      }
    }
  }

  /**
   * Initialize design tokens from the design system data
   * This method creates editable tokens for colors, typography, and buttons
   */
  private initializeDesignTokens(): void {
    // Use centralized design tokens data
    this.designTokens = [...ALL_DESIGN_TOKENS];

    // Log token statistics for debugging
    this.logger.info('Design tokens initialized:', {
      total: DESIGN_TOKENS_STATS.total,
      colors: DESIGN_TOKENS_STATS.colors,
      typography: DESIGN_TOKENS_STATS.typography,
      buttons: DESIGN_TOKENS_STATS.buttons,
      categories: DESIGN_TOKENS_STATS.categories
    });

    // If we have actual design system data from API response, merge it
    if (this.designSystemData) {
      this.parseDesignSystemData();
    }

    // View will update automatically via reactive patterns
  }

  /**
   * Add default color tokens when no design system data is available
   */
  private addDefaultColorTokens(): void {
    const defaultColors: ColorToken[] = [
       { name: 'D_Yellow', value: '#E48900', hexCode: '#E48900' },
      { name: 'Lemon', value: '#FFA826', hexCode: '#FFA826' },
      { name: 'Black', value: '#212121', hexCode: '#212121' },
      { name: 'D_Grey', value: '#4D4D4D', hexCode: '#4D4D4D' },
      { name: 'Silver', value: '#F5F7FA', hexCode: '#F5F7FA' }
    ];

    // Add color tokens to the design tokens array
    defaultColors.forEach((color, index) => {
      this.designTokens.push({
        id: `color-${index}`,
        name: color.name,
        value: color.value,
        type: 'color',
        category: 'Colors',
        editable: true
      });
    });
  }

  /**
   * Add default typography tokens when no design system data is available
   */
  private addDefaultTypographyTokens(): void {
    const defaultFontStyles: FontStyleToken[] = [
      { name: 'Headline 1', size: '64/76', weight: 'Semi Bold', lineHeight: '1.2' },
      { name: 'Headline 2', size: '36/44', weight: 'Semi Bold', lineHeight: '1.2' },
      { name: 'Headline 3', size: '28/36', weight: 'Semi Bold', lineHeight: '1.3' },
      { name: 'Headline 4', size: '20/28', weight: 'Semi Bold', lineHeight: '1.4' }
    ];

    // Add typography tokens to the design tokens array
    defaultFontStyles.forEach((font, index) => {
      this.designTokens.push({
        id: `typography-${index}`,
        name: font.name,
        value: `${font.weight}, ${font.size}`,
        type: 'typography',
        category: 'Font Style Desktop',
        editable: true
      });
    });
  }

  /**
   * Add default button tokens when no design system data is available
   */
  private addDefaultButtonTokens(): void {
    const defaultButtons: ButtonToken[] = [
      { label: 'Label', variant: 'primary' },
      { label: 'Label', variant: 'primary', hasIcon: true },
      { label: 'Label', variant: 'secondary' },
      { label: 'Label', variant: 'outline' },
      { label: 'Label', variant: 'text' }
    ];

    // Add button tokens to the design tokens array
    defaultButtons.forEach((button, index) => {
      this.designTokens.push({
        id: `button-${index}`,
        name: button.label,
        value: button.variant,
        type: 'size',
        category: 'Buttons',
        editable: true
      });
    });
  }
/**
   * Generate a color name based on a hex value
   * @param hexColor The hex color value
   * @returns A generated color name
   */
  generateColorName(hexColor: string): string {
    // Remove # if present
    const hex = hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;

    // Basic color mapping for common colors
    const colorMap: {[key: string]: string} = {
      '000000': 'Black',
      'FFFFFF': 'White',
      'FF0000': 'Red',
      '00FF00': 'Green',
      '0000FF': 'Blue',
      'FFFF00': 'Yellow',
      '00FFFF': 'Cyan',
      'FF00FF': 'Magenta',
      'C0C0C0': 'Silver',
      '808080': 'Gray',
      '800000': 'Maroon',
      '808000': 'Olive',
      '008000': 'Dark Green',
      '800080': 'Purple',
      '008080': 'Teal',
      '000080': 'Navy',
      'FFA500': 'Orange',
      'A52A2A': 'Brown',
      'FFC0CB': 'Pink',
      'E48900': 'D_Yellow',
      'FFA826': 'Lemon',
      '212121': 'Black',
      '4D4D4D': 'D_Grey',
      'F5F7FA': 'Silver'
    };

    // Check if the color is in our map
    if (colorMap[hex.toUpperCase()]) {
      return colorMap[hex.toUpperCase()];
    }

    // Parse the hex color to RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Determine the hue
    let hue = '';
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);

    if (max === min) {
      // It's a shade of gray
      const brightness = Math.round((r + g + b) / 3);
      if (brightness < 64) return 'Dark Gray';
      if (brightness < 128) return 'Gray';
      if (brightness < 192) return 'Light Gray';
      return 'Off White';
    }

    if (r === max) {
      if (g > b) hue = 'Orange';
      else hue = 'Red';
    } else if (g === max) {
      if (r > b) hue = 'Yellow Green';
      else hue = 'Green';
    } else {
      if (r > g) hue = 'Purple';
      else hue = 'Blue';
    }

    // Determine brightness
    const brightness = (r + g + b) / 3;
    let prefix = '';

    if (brightness < 85) prefix = 'Dark ';
    else if (brightness > 170) prefix = 'Light ';

    return prefix + hue;
  }
  /**
   * Parse design system data from API response
   */
  private parseDesignSystemData(): void {
    // This would parse actual design system data from the API
    // For now, we'll use the default tokens
    this.addDefaultColorTokens();
    this.addDefaultTypographyTokens();
    this.addDefaultButtonTokens();
  }

  /**
   * Update a design token value
   * @param tokenId The ID of the token to update
   * @param newValue The new value for the token
   */
  updateDesignToken(tokenId: string, newValue: string): void {
    // Find the token by ID
    const tokenIndex = this.designTokens.findIndex(token => token.id === tokenId);

    if (tokenIndex !== -1) {
      const token = this.designTokens[tokenIndex];

      // Update the token value
      token.value = newValue;

      // If it's a color token, update the name based on the color
      if (token.type === 'color') {
        token.name = this.generateColorName(newValue);
      }

      // Send updated tokens to backend when color values change
      this.sendDesignTokensToBackend();

      // View will update automatically via reactive patterns
    }
  }

  /**
   * Get all design tokens in a format suitable for backend communication
   * @returns Formatted design tokens object
   */
  getDesignTokensForBackend(): any {
    const tokensByCategory = {
      colors: this.designTokens
        .filter(token => token.type === 'color')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          hexCode: token.value,
          category: token.category,
          editable: token.editable
        })),
      typography: this.designTokens
        .filter(token => token.type === 'typography')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          category: token.category,
          editable: token.editable
        })),
      buttons: this.designTokens
        .filter(token => token.category === 'Buttons')
        .map(token => ({
          id: token.id,
          name: token.name,
          variant: token.value,
          category: token.category,
          editable: token.editable
        }))
    };

    return {
      designSystem: {
        tokens: tokensByCategory,
        timestamp: new Date().toISOString(),
        projectId: this.projectId,
        jobId: this.jobId
      }
    };
  }

  // Debounce timer for design tokens updates
  private designTokensUpdateTimer: any;

  /**
   * Send design tokens to backend for processing (with debouncing)
   */
  private sendDesignTokensToBackend(): void {
    if (!this.projectId || !this.jobId) {
      this.logger.warn('Cannot send design tokens: missing project ID or job ID');
      return;
    }

    // Clear existing timer to debounce rapid changes
    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
    }

    // Set a new timer to send the update after 1 second of no changes
    this.designTokensUpdateTimer = setTimeout(() => {
      const tokensPayload = this.getDesignTokensForBackend();

      this.logger.info('Sending design tokens to backend:', tokensPayload);

      // Send the tokens update request
      this.sendTokensUpdateRequest(tokensPayload);
    }, 1000); // 1 second debounce
  }

  /**
   * Send tokens update request to backend
   * @param tokensPayload The design tokens payload
   */
  private sendTokensUpdateRequest(tokensPayload: any): void {
    // Create the request payload for design tokens update
    const updateRequest = {
      project_id: this.projectId,
      job_id: this.jobId,
      action: 'update_design_tokens',
      design_tokens: tokensPayload.designSystem.tokens,
      timestamp: tokensPayload.designSystem.timestamp
    };

    // Send to backend using the code generation service
    this.codeGenerationService.updateDesignTokens(updateRequest).subscribe({
      next: (response) => {
        this.logger.info('Design tokens updated successfully:', response);
        this.toastService.success('Design tokens updated');
      },
      error: (error) => {
        this.logger.error('Failed to update design tokens:', error);
        this.toastService.error('Failed to update design tokens');
      }
    });
  }

  /**
   * Manually trigger design tokens update (useful for testing or immediate updates)
   */
  triggerDesignTokensUpdate(): void {
    // Clear any pending timer
    if (this.designTokensUpdateTimer) {
      clearTimeout(this.designTokensUpdateTimer);
      this.designTokensUpdateTimer = null;
    }

    // Send immediately
    if (this.projectId && this.jobId) {
      const tokensPayload = this.getDesignTokensForBackend();
      this.logger.info('Manually triggering design tokens update:', tokensPayload);
      this.sendTokensUpdateRequest(tokensPayload);
    } else {
      this.logger.warn('Cannot trigger design tokens update: missing project ID or job ID');
    }
  }

  /**
   * Get the current state of design tokens for debugging or external use
   */
  getCurrentDesignTokensState(): any {
    return {
      tokens: this.designTokens,
      hasDesignSystem: this.hasDesignSystem,
      projectId: this.projectId,
      jobId: this.jobId,
      backendPayload: this.getDesignTokensForBackend()
    };
  }

  /**
   * Check if a string is a valid hex color
   * @param color The color string to validate
   * @returns True if the color is a valid hex color
   */
  isValidHexColor(color: string): boolean {
    // Use the centralized validation function
    return validateHexColor(color);
  }

  /**
   * Handle token value change from input element
   * @param event The change event
   * @param tokenId The ID of the token to update
   */
onTokenValueChange(event: Event, tokenId: string): void {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement && inputElement.value) {
      const value = inputElement.value;

      // For color tokens, ensure it's a valid hex color
      const token = this.designTokens.find(t => t.id === tokenId);
      if (token && token.type === 'color') {
        // Check if it's a valid hex color
        const isValid = this.isValidHexColor(value);

        // Add or remove the invalid class based on validation
        if (isValid) {
          inputElement.classList.remove('invalid');

          // Ensure the value has a # prefix
          const formattedValue = value.startsWith('#') ? value : `#${value}`;
          this.updateDesignToken(tokenId, formattedValue);
        } else {
          inputElement.classList.add('invalid');
          // Don't update the token if the value is invalid
          return;
        }
      } else {
        // For non-color tokens, update as is
        this.updateDesignToken(tokenId, value);
      }
    }
  }

  /**
   * Get design tokens filtered by category
   * @param category The category to filter by
   * @returns Filtered array of design tokens
   */
  getTokensByCategory(category: string): DesignToken[] {
    // Use the centralized helper function with current tokens
    return this.designTokens.filter(token => token.category === category);
  }

  /**
   * Get the file icon class based on file type
   * @param fileType The type of the file
   * @returns The CSS class for the file icon
   */
  getFileIconClass(fileType: string): string {
    switch (fileType) {
      case 'markdown':
        return 'file-icon-md';
      case 'image':
        return 'file-icon-img';
      case 'svg':
        return 'file-icon-svg';
      case 'text':
        return 'file-icon-txt';
      case 'component':
        return 'file-icon-component';
      default:
        return 'file-icon-default';
    }
  }

  // Index of the last displayed log message for streaming effect
  lastDisplayedLogIndex = 0;

  /**
   * Returns the appropriate CSS class for a log message based on its content
   * @param log The log message to analyze
   * @returns CSS class name for styling the log
   */
  getLogClass(log: string | any): string {
    // If the log is an object with a type property, use that
    if (typeof log === 'object' && log !== null && log.type) {
      switch (log.type) {
        case 'error':
          return 'log-error';
        case 'warning':
          return 'log-warning';
        case 'debug':
          return 'log-debug';
        case 'code':
          return 'log-code';
        case 'progress-description':
          return 'log-info progress-description';
        case 'status-change':
          return 'log-info status-change';
        default:
          return 'log-info';
      }
    }

    // Otherwise, analyze the string content
    if (typeof log === 'string') {
      if (log.includes('ERROR')) {
        return 'log-error';
      } else if (log.includes('WARN')) {
        return 'log-warning';
      } else if (log.includes('DEBUG')) {
        return 'log-debug';
      } else if (log.includes('Progress Description')) {
        // Highlight progress description logs
        return 'log-info progress-description';
      } else if (log.includes('Status changed to:')) {
        // Highlight status change logs
        return 'log-info status-change';
      } else {
        return 'log-info';
      }
    }

    return 'log-info';
  }

  @Input() defaultLeftPanelWidth: string = '50%';
  @Input() defaultRightPanelWidth: string = '50%';

  toggleLeftPanel(): void {
    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const splitScreen = document.querySelector('.awe-splitscreen') as HTMLElement;

    if (leftPanel && rightPanel && splitScreen) {
      if (this.isLeftPanelCollapsed$.value) {
        // Expand left panel (return to default position)
        leftPanel.style.width = this.defaultLeftPanelWidth;
        rightPanel.style.width = this.defaultRightPanelWidth;
        this.isLeftPanelCollapsed$.next(false);
        // Remove the class that hides the resizer
        splitScreen.classList.remove('left-panel-collapsed');
      } else {
        // Collapse left panel
        leftPanel.style.width = '0%';
        rightPanel.style.width = '100%';
        this.isLeftPanelCollapsed$.next(true);
        // Add the class to hide the resizer
        splitScreen.classList.add('left-panel-collapsed');
      }
    }
  }


  // Property to store the selected image data URI (already defined above)

  // Icons for the chat window
  rightIcons: { name: string; status: IconStatus }[] = [
    { name: 'awe_enhanced_alternate', status: 'default' },
    { name: 'awe_enhance', status: 'active' },
    { name: 'awe_enhanced_send', status: 'active' },
  ];

  // Update icons based on theme

  handleIconClick(event: { name: string; side: string; index: number; theme: string }): void {
    const normalizedIconName = event.name.toLowerCase();
    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        this.handleEnhancedAlternate();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        if (event.theme === 'dark') {
          this.handleEnhancedSendDark();
        } else if (event.theme === 'light') {
          this.handleEnhancedSendLight();
        }
        break;
    }
  }

  handleEnhancedSendDark(): void {
    if (!this.darkPrompt.trim()) return;

    this.darkMessages.push({ text: this.darkPrompt, from: 'user', theme: 'dark' });

    setTimeout(() => {
      this.darkMessages.push({
        text: 'This is an AI-generated reply to your message (dark theme).',
        from: 'ai',
        theme: 'dark',
      });
    }, 100);

    this.darkPrompt = '';
  }

  // Flag to track if AI is responding
  isAiResponding: boolean = false;

  /**
   * Handles the send button click or Enter key press in the chat window
   * If an element is selected, it will send a modification request
   * Otherwise, it will handle the message as a regular chat message
   */
  handleEnhancedSendLight(): void {
    if (!this.lightPrompt.trim()) return;
    if (this.isAiResponding) return; // Prevent sending while AI is responding

    // Get user signature for any API calls (not used now but may be needed in future)
    // const userSignature = this.userSignatureService.getUserSignatureSync();

    // Check if we have a selected element and need to handle element modification
    if (this.selectedElementHTML && this.selectedElementPath) {
      // Handle element modification request
      this.sendElementModificationRequest(this.lightPrompt);

      // Clear the prompt
      this.lightPrompt = '';

      return;
    }

    // Store the current image data URI for this message
    const currentImageDataUri = this.selectedImageDataUri;

    // Regular message handling
    this.isAiResponding = true; // Set flag to indicate AI is responding
    this.lightMessages.push({
      text: this.lightPrompt,
      from: 'user',
      theme: 'light',
      imageDataUri: currentImageDataUri || undefined
    });

    // Clear the image data URI after adding it to the message
    // This ensures it's only shown for the first prompt
    if (currentImageDataUri) {
      this.selectedImageDataUri = '';
    }

    // Clear the prompt input
    this.lightPrompt = '';

    // Add initial message
    setTimeout(() => {
      this.lightMessages.push({
        text: 'Analyzing your request...',
        from: 'ai',
        theme: 'light',
      });
    }, 500);

    // Get the current project state to include user selections
    this.appStateService.project$
      .subscribe(projectState => {
        // Create a summary of user selections
        let userSelectionsSummary = '';

        // Add image information if available
        if (projectState.imageUrl) {
          userSelectionsSummary += '- **Image**: You provided an image for reference\n';
        }

        // Add technology information if available
        if (projectState.technology) {
          userSelectionsSummary += `- **Technology**: ${projectState.technology.charAt(0).toUpperCase() + projectState.technology.slice(1)}\n`;
        }

        // Add design library information if available
        if (projectState.designLibrary) {
          userSelectionsSummary += `- **Design Library**: ${projectState.designLibrary.charAt(0).toUpperCase() + projectState.designLibrary.slice(1)}\n`;
        }

        // Add application type information if available
        if (projectState.application) {
          userSelectionsSummary += `- **Application Type**: ${projectState.application.charAt(0).toUpperCase() + projectState.application.slice(1)}\n`;
        }

        // Add type information if available
        if (projectState.type) {
          userSelectionsSummary += `- **Generation Type**: ${projectState.type}\n`;
        }

        // Comprehensive project overview in a single message
        setTimeout(() => {
          // Update the last message instead of adding a new one
          const lastMessage = this.lightMessages[this.lightMessages.length - 1];
          if (lastMessage && lastMessage.from === 'ai') {
            // Get the user's request from the previous message (for context)
            // const userPrompt = this.lightMessages[this.lightMessages.length - 2]?.text || 'your request';

            lastMessage.text = `Hang tight! We're currently working on the edit functionality.You can view the code, preview & logs on the right side.`;
          }
          this.isAiResponding = false; // Reset the flag when AI is done responding
        }, 2000);
      })
      .unsubscribe(); // Unsubscribe immediately since we only need the current value
  }

  handleEnhancedAlternate(): void {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';

    fileInput.addEventListener('change', (event: Event) => {
      const input = event.target as HTMLInputElement;
      if (input.files?.length) {
        alert('File uploaded: ' + input.files[0].name);
      }
    });

    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  }

  handleEnhanceText(): void {
    if (!this.lightPrompt.trim()) {
      return;
    }

    this.rightIcons[1].status = 'disable'; // Disable the enhance button while enhancing
    const originalPrompt = this.lightPrompt;

    // Add a message to show that enhancement is in progress
    this.lightMessages.push({
      text: 'Enhancing your prompt to provide more detailed instructions...',
      from: 'ai',
      theme: 'light',
    });

    // Call the prompt service to enhance the prompt
    this.promptService.enhancePrompt(this.lightPrompt, 'Generate Application').subscribe({
      next: (response: any) => {
        if (response && response.code && response.status_code === 200) {
          // Update the prompt with the enhanced version
          this.lightPrompt = response.code;

          // Add a message to show the enhanced prompt
          this.lightMessages.push({
            text:
              "I've enhanced your prompt to provide more detailed instructions. Here's the enhanced version:\n\n" +
              response.code,
            from: 'ai',
            theme: 'light',
          });

          this.logger.debug('Original prompt:', originalPrompt);
          this.logger.debug('Enhanced Prompt', this.lightPrompt);
        } else {
          // Add a message to show that enhancement failed
          this.lightMessages.push({
            text: "I couldn't enhance your prompt. Please try again with more details.",
            from: 'ai',
            theme: 'light',
          });
          this.logger.error('Could not enhance prompt. Please try again.');
        }
      },
      error: (error: any) => {
        this.logger.error('Error enhancing prompt:', error);
        // Add a message to show that enhancement failed
        this.lightMessages.push({
          text: 'I encountered an error while enhancing your prompt. Please try again.',
          from: 'ai',
          theme: 'light',
        });
      },
      complete: () => {
        this.rightIcons[1].status = 'active'; // Re-enable the enhance button
      },
    });
  }

  onIconClick(_iconName: string): void {
    // Your existing logic
    // Unused parameter prefixed with underscore
  }

  // isTryToFixButtonVisible(): boolean {
  //   return !this.tryToFixClicked && this.status === 'FAILED';
  // }


  /**
   * Toggle the export modal
   */
  toggleExportModal(): void {
    this.logger.info('toggleExportModal called', {
      isCodeGenerationComplete: this.isCodeGenerationComplete,
      previewError: this.previewError,
      isExperienceStudioModalOpen: this.isExperienceStudioModalOpen
    });

    // No validation - allow opening the modal regardless of code generation status
    this.isExperienceStudioModalOpen$.next(!this.isExperienceStudioModalOpen$.value);

    // If we're opening the modal, log it
    if (this.isExperienceStudioModalOpen$.value) {
      this.logger.info('Export modal opened');
      this.logger.info('Export modal opened');
    } else {
      this.logger.info('Export modal closed');
      this.logger.info('Export modal closed');
    }

    // View will update automatically via reactive patterns
  }

  /**
   * Copy text to clipboard
   * @param inputElement The input element containing the text to copy
   */
  copyToClipboard(inputElement: HTMLInputElement): void {
    this.logger.info('Copying link to clipboard');

    try {
      inputElement.select();
      document.execCommand('copy');
      inputElement.setSelectionRange(0, 0); // Deselect

      // Show toast notification
      this.toastService.success('Link copied to clipboard');
      this.logger.info('Link copied to clipboard');
    } catch (error) {
      this.logger.error('Failed to copy link to clipboard', error);
      this.toastService.error('Failed to copy link to clipboard');
    }
  }

  /**
   * Export to VSCode
   */
  exportToVSCode(): void {
    this.logger.info('Exporting to VSCode');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Preparing VSCode export...');

    // In a real implementation, this would trigger the VSCode export process
    setTimeout(() => {
      this.toastService.success('Project exported to VSCode');
      this.logger.info('Project exported to VSCode');
    }, 1500);
  }

  /**
   * Export to Azure
   */
  exportToAzure(): void {
    this.logger.info('Exporting to Azure');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Preparing Azure export...');

    // In a real implementation, this would trigger the Azure export process
    setTimeout(() => {
      this.toastService.success('Project exported to Azure');
      this.logger.info('Project exported to Azure');
    }, 1500);
  }

  /**
   * Download project as a zip file with proper tree structure
   */
  async downloadProject(): Promise<void> {
    this.logger.info('Downloading project as zip file');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Preparing download...');

    // Get the generated code from the code sharing service
    const generatedCode = this.codeSharingService.getGeneratedCode();

    if (!generatedCode) {
      this.toastService.error('No code available to download');
      return;
    }

    try {
      // Generate app name for the zip file
      const appName = this.generateAppNameForDownload();

      // Create a new JSZip instance
      const zip = new JSZip();

      // Create the main project folder
      const projectFolder = zip.folder(appName);

      if (!projectFolder) {
        throw new Error('Failed to create project folder');
      }

      // Process the generated code and add files to zip
      await this.addFilesToZip(projectFolder, generatedCode);

      // Add additional project files
      this.addProjectMetadata(projectFolder, appName);

      // Generate the zip file
      this.toastService.info('Creating zip file...');
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      });

      // Create download link
      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${appName}.zip`;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      // Show success notification
      this.toastService.success(`Project "${appName}" downloaded successfully`);
      this.logger.info('Project zip file downloaded successfully', { appName });
    } catch (error) {
      this.logger.error('Error downloading project:', error);
      this.toastService.error('Error creating project download');
    }
  }

  /**
   * Generate app name for download
   */
  private generateAppNameForDownload(): string {
    // Try to get app name from various sources
    if (this.appName) {
      return this.appName;
    }

    if (this.projectName) {
      return this.projectName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    }

    // Get from prompt service or generate based on current data
    const promptData = this.promptService.getCurrentPromptData();
    if (promptData?.selectedCardTitle) {
      const baseName = promptData.selectedCardTitle.toLowerCase().replace(/[^a-z0-9]/g, '-');
      return `${baseName}-app`;
    }

    // Fallback to timestamp-based name
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `generated-app-${timestamp}`;
  }

  /**
   * Add files to zip with proper tree structure
   */
  private async addFilesToZip(projectFolder: JSZip, generatedCode: any): Promise<void> {
    if (typeof generatedCode === 'string') {
      // Handle string content - try to parse as JSON first
      try {
        const parsedCode = JSON.parse(generatedCode);
        await this.processCodeObject(projectFolder, parsedCode);
      } catch {
        // If not JSON, treat as single file
        projectFolder.file('index.html', generatedCode);
      }
    } else if (Array.isArray(generatedCode)) {
      // Handle array of files
      for (const item of generatedCode) {
        if (typeof item === 'object' && item !== null) {
          const fileName = item.fileName || item.name || item.path || 'unknown.txt';
          const content = item.content || '';
          this.addFileToZipWithPath(projectFolder, fileName, content);
        }
      }
    } else if (typeof generatedCode === 'object' && generatedCode !== null) {
      // Handle object with file paths as keys
      await this.processCodeObject(projectFolder, generatedCode);
    }
  }

  /**
   * Process code object and add files with proper paths
   */
  private async processCodeObject(projectFolder: JSZip, codeObject: any): Promise<void> {
    for (const [filePath, content] of Object.entries(codeObject)) {
      const fileContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
      this.addFileToZipWithPath(projectFolder, filePath, fileContent);
    }
  }

  /**
   * Add file to zip with proper directory structure
   */
  private addFileToZipWithPath(projectFolder: JSZip, filePath: string, content: string): void {
    // Clean the file path
    const cleanPath = filePath.replace(/^\/+/, ''); // Remove leading slashes

    // Split path into directories and filename
    const pathParts = cleanPath.split('/');
    const fileName = pathParts.pop() || 'unknown.txt';

    // Create nested folders if needed
    let currentFolder = projectFolder;
    for (const folderName of pathParts) {
      if (folderName.trim()) {
        const existingFolder = currentFolder.folder(folderName);
        currentFolder = existingFolder || currentFolder.folder(folderName)!;
      }
    }

    // Add the file to the appropriate folder
    currentFolder.file(fileName, content);
  }

  /**
   * Add project metadata files
   */
  private addProjectMetadata(projectFolder: JSZip, appName: string): void {
    // Create README.md
    const readmeContent = this.generateReadmeContent(appName);
    projectFolder.file('README.md', readmeContent);

    //commented out for now-> if need in future we can use to add the package and gitignore to the download zip folder.

    // // Create package.json if it doesn't exist
    // const hasPackageJson = this.checkIfFileExists(projectFolder, 'package.json');
    // if (!hasPackageJson) {
    //   const packageJsonContent = this.generatePackageJsonContent(appName);
    //   projectFolder.file('package.json', packageJsonContent);
    // }

    // Create .gitignore if it doesn't exist
    // const hasGitignore = this.checkIfFileExists(projectFolder, '.gitignore');
    // if (!hasGitignore) {
    //   const gitignoreContent = this.generateGitignoreContent();
    //   projectFolder.file('.gitignore', gitignoreContent);
    // }
  }

  /**
   * Check if a file exists in the zip folder
   */
  private checkIfFileExists(folder: JSZip, fileName: string): boolean {
    return folder.file(fileName) !== null;
  }

  /**
   * Generate README.md content
   */
  private generateReadmeContent(appName: string): string {
    const currentDate = new Date().toLocaleDateString();
    return `# ${appName}

Generated on: ${currentDate}

## Description
This project was generated using the Experience Studio platform.

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn

### Installation
\`\`\`bash
npm install
\`\`\`

### Running the Application
\`\`\`bash
npm start
\`\`\`

### Building for Production
\`\`\`bash
npm run build
\`\`\`

## Project Structure
- \`src/\` - Source code files
- \`public/\` - Static assets

## Support
For support and questions, please refer to the Experience Studio documentation.
`;
  }

  /**
   * Generate package.json content
   */
  // private generatePackageJsonContent(appName: string): string {
  //   const packageJson = {
  //     name: appName,
  //     version: "1.0.0",
  //     description: "Generated by Experience Studio",
  //     main: "index.html",
  //     scripts: {
  //       start: "npx serve .",
  //       build: "echo 'Build completed'",
  //       dev: "npx serve . -l 3000"
  //     },
  //     keywords: ["experience-studio", "generated", "web-app"],
  //     author: "Experience Studio",
  //     license: "MIT",
  //     devDependencies: {
  //       serve: "^14.0.0"
  //     }
  //   };

  //   return JSON.stringify(packageJson, null, 2);
  // }

  /**
   * Generate .gitignore content
   */
//   private generateGitignoreContent(): string {
//     return `# Dependencies
// node_modules/
// npm-debug.log*
// yarn-debug.log*
// yarn-error.log*

// # Production builds
// /build
// /dist

// # Environment variables
// .env
// .env.local
// .env.development.local
// .env.test.local
// .env.production.local

// # IDE files
// .vscode/
// .idea/
// *.swp
// *.swo

// # OS generated files
// .DS_Store
// .DS_Store?
// ._*
// .Spotlight-V100
// .Trashes
// ehthumbs.db
// Thumbs.db

// # Logs
// logs
// *.log

// # Runtime data
// pids
// *.pid
// *.seed
// *.pid.lock

// # Coverage directory used by tools like istanbul
// coverage/

// # Temporary folders
// tmp/
// temp/
// `;
//   }

  /**
   * Generate unit tests
   */
  generateUnitTests(): void {
    this.logger.info('Generating unit tests');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Generating unit tests...');

    // In a real implementation, this would trigger the unit test generation process
    setTimeout(() => {
      this.toastService.success('Unit tests generated successfully');
      this.logger.info('Unit tests generated successfully');
    }, 2000);
  }

  /**
   * Generate API
   */
  generateAPI(): void {
    this.logger.info('Generating API');

    // Close the modal
    this.isExperienceStudioModalOpen$.next(false);

    // Show toast notification
    this.toastService.info('Generating API...');

    // In a real implementation, this would trigger the API generation process
    setTimeout(() => {
      this.toastService.success('API generated successfully');
      this.logger.info('API generated successfully');
    }, 2000);
  }

  /**
   * Handle tab click events
   * @param tab The name of the tab that was clicked
   */
  onTabClick(tab: string): void {
    // Set flag to indicate user has manually selected a tab
    this.userSelectedTab = true;

    // Handle tab clicks based on the tab name
    switch (tab) {
      case 'preview':
        this.togglePreviewView();
        break;
      case 'code':
        // Only toggle code view if code is generated and there's no error
        if (this.isCodeGenerationComplete && !this.previewError$.value) {
          this.toggleCodeView();
        }
        break;
      case 'logs':
        // Only toggle logs view if logs are available
        if (this.hasLogs) {
          this.toggleLogsView();
        }
        break;
      case 'artifacts':
        // Only toggle artifacts view if artifacts tab is enabled
        if (this.isArtifactsTabEnabled) {
          this.toggleArtifactsView();
        }
        break;
    }
  }

  @HostListener('window:message', ['$event'])
  onMessage(event: MessageEvent) {
    if (event.data && event.data.type === 'elementSelected') {
      this.selectedElement = event.data.element;
      this.showEditorIcon = true;
    }
  }

  /**
   * Handles iframe load event
   * @param _event The load event (unused but kept for future implementation)
   */
  onIframeLoad(_event: Event): void {

    // If we're in element selection mode, inject the selection scripts
    if (this.isElementSelectionMode) {
      this.injectSelectionScripts();
    }
  }

  /**
   * Toggles element selection mode on/off
   */
  toggleElementSelectionMode(): void {
    this.isElementSelectionMode = !this.isElementSelectionMode;

    // If we're turning on selection mode, make sure we're in preview view
    if (this.isElementSelectionMode) {
      if (this.currentView$.value !== 'preview') {
        this.togglePreviewView();
      }

      // Inject selection scripts into the iframe
      this.injectSelectionScripts();

      // Add a message to the chat window
      // this.lightMessages.push({
      //   text: "I've enabled element selection mode. Hover over any element in the preview and click to select it for editing.",
      //   from: 'ai',
      //   theme: 'light'
      // });
    } else {
      // Clean up selection mode
      this.cleanupSelectionMode();

      // If an element was selected, clear it
      if (this.selectedElement) {
        this.clearSelectedElement();
      }
    }

    // Force change detection
    this.cdr.detectChanges();
  }

  /**
   * Injects scripts into the iframe to enable element selection
   */
  private injectSelectionScripts(): void {
    try {
      // Get the iframe element
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (!iframe || !iframe.contentWindow) {
        this.logger.error('Cannot access iframe content window');
        return;
      }

      // Check if we can access the contentDocument (cross-origin check)
      let isCrossOrigin = false;
      try {
        // This will throw an error if cross-origin
        const testAccess = iframe.contentDocument;
        if (!testAccess) {
          this.logger.error('Cannot access iframe content document');
          isCrossOrigin = true;
        }
      } catch (e) {
        this.logger.warn('Cross-origin iframe detected, using postMessage for communication');
        isCrossOrigin = true;
      }

      // If cross-origin, use postMessage to inject scripts
      if (isCrossOrigin) {
        // Send a message to the iframe to initialize element selection
        iframe.contentWindow.postMessage({
          type: 'initElementSelection'
        }, '*');

        // Add event listener for messages from the iframe
        window.addEventListener('message', this.handleIframeMessage);
        return;
      }

      // If we can access the contentDocument directly (same-origin)
      if (!iframe.contentDocument) {
        this.logger.error('Cannot access iframe content document');
        return;
      }

      // Create a style element for highlighting
      const style = iframe.contentDocument.createElement('style');
      style.id = 'element-selection-styles';
      style.textContent = `
        .element-hover {
          outline: 2px dashed #007bff !important;
          outline-offset: 2px !important;
          cursor: pointer !important;
          position: relative !important;
        }

        .element-selected {
          outline: 3px solid #28a745 !important;
          outline-offset: 3px !important;
          position: relative !important;
        }

        .element-tooltip {
          position: fixed;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          z-index: 10000;
          pointer-events: none;
        }
      `;

      // Add the style to the iframe document
      iframe.contentDocument.head.appendChild(style);

      // Create a script element for the selection logic
      const script = iframe.contentDocument.createElement('script');
      script.id = 'element-selection-script';
      script.textContent = `
        (function() {
          let hoveredElement = null;
          let tooltip = null;

          // Create tooltip element
          function createTooltip() {
            tooltip = document.createElement('div');
            tooltip.className = 'element-tooltip';
            document.body.appendChild(tooltip);
          }

          // Update tooltip position and content
          function updateTooltip(element, event) {
            if (!tooltip) createTooltip();

            const tagName = element.tagName.toLowerCase();
            const id = element.id ? '#' + element.id : '';
            const classes = Array.from(element.classList)
              .filter(cls => cls !== 'element-hover' && cls !== 'element-selected')
              .map(cls => '.' + cls)
              .join('');

            tooltip.textContent = tagName + id + classes;
            tooltip.style.top = (event.clientY + 15) + 'px';
            tooltip.style.left = (event.clientX + 10) + 'px';
          }

          // Hide tooltip
          function hideTooltip() {
            if (tooltip) {
              tooltip.style.display = 'none';
            }
          }

          // Show tooltip
          function showTooltip() {
            if (tooltip) {
              tooltip.style.display = 'block';
            }
          }

          // Handle mouseover event
          function handleMouseOver(event) {
            // Skip if the target is the body or html element
            if (event.target === document.body || event.target === document.documentElement) {
              return;
            }

            // Remove hover class from previous element
            if (hoveredElement && hoveredElement !== event.target) {
              hoveredElement.classList.remove('element-hover');
            }

            // Add hover class to current element
            hoveredElement = event.target;
            hoveredElement.classList.add('element-hover');

            // Update tooltip
            updateTooltip(hoveredElement, event);
            showTooltip();

            // Stop event propagation
            event.stopPropagation();
          }

          // Handle mousemove event
          function handleMouseMove(event) {
            if (hoveredElement) {
              updateTooltip(hoveredElement, event);
            }
          }

          // Handle mouseout event
          function handleMouseOut(event) {
            // Only remove the hover class if we're leaving the element
            if (hoveredElement && !hoveredElement.contains(event.relatedTarget)) {
              hoveredElement.classList.remove('element-hover');
              hoveredElement = null;
              hideTooltip();
            }

            event.stopPropagation();
          }

          // Handle click event
          function handleClick(event) {
            // Skip if the target is the body or html element
            if (event.target === document.body || event.target === document.documentElement) {
              return;
            }

            // Get the clicked element
            const element = event.target;

            // Remove hover class
            element.classList.remove('element-hover');

            // Add selected class
            element.classList.add('element-selected');

            // Get element details
            const tagName = element.tagName.toLowerCase();
            const id = element.id || null;
            const classes = Array.from(element.classList)
              .filter(cls => cls !== 'element-hover' && cls !== 'element-selected')
              .join(' ');

            // Get element HTML
            const outerHTML = element.outerHTML;

            // Get computed styles
            const computedStyle = window.getComputedStyle(element);
            const cssProperties = {};
            for (let i = 0; i < computedStyle.length; i++) {
              const prop = computedStyle[i];
              cssProperties[prop] = computedStyle.getPropertyValue(prop);
            }

            // Get element path
            const getElementPath = (el) => {
              if (!el) return '';
              if (el === document.body) return 'body';

              let path = '';
              let current = el;

              while (current && current !== document.body) {
                let selector = current.tagName.toLowerCase();

                if (current.id) {
                  selector += '#' + current.id;
                } else {
                  const siblings = Array.from(current.parentNode.children)
                    .filter(child => child.tagName === current.tagName);

                  if (siblings.length > 1) {
                    const index = siblings.indexOf(current) + 1;
                    selector += ':nth-of-type(' + index + ')';
                  }
                }

                path = selector + (path ? ' > ' + path : '');
                current = current.parentNode;
              }

              return 'body > ' + path;
            };

            const elementPath = getElementPath(element);

            // Send message to parent window
            window.parent.postMessage({
              type: 'elementSelected',
              data: {
                tagName,
                id,
                classes,
                html: outerHTML,
                css: cssProperties,
                path: elementPath
              }
            }, '*');

            // Stop event propagation and prevent default
            event.stopPropagation();
            event.preventDefault();
          }

          // Add event listeners
          document.addEventListener('mouseover', handleMouseOver, true);
          document.addEventListener('mousemove', handleMouseMove, true);
          document.addEventListener('mouseout', handleMouseOut, true);
          document.addEventListener('click', handleClick, true);

          // Function to clean up element selection
          function cleanupElementSelection() {
            document.removeEventListener('mouseover', handleMouseOver, true);
            document.removeEventListener('mousemove', handleMouseMove, true);
            document.removeEventListener('mouseout', handleMouseOut, true);
            document.removeEventListener('click', handleClick, true);

            // Remove any hover or selected classes
            const hovered = document.querySelector('.element-hover');
            if (hovered) hovered.classList.remove('element-hover');

            const selected = document.querySelector('.element-selected');
            if (selected) selected.classList.remove('element-selected');

            // Remove tooltip
            if (tooltip) {
              document.body.removeChild(tooltip);
              tooltip = null;
            }

            // Remove style and script elements
            const style = document.getElementById('element-selection-styles');
            if (style) style.parentNode.removeChild(style);

            const script = document.getElementById('element-selection-script');
            if (script) script.parentNode.removeChild(script);
          }

          // Store the cleanup function for direct access (same-origin case)
          window._elementSelectionCleanup = cleanupElementSelection;

          // Also listen for cleanup messages (cross-origin case)
          window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'cleanupElementSelection') {

              cleanupElementSelection();
            }
          });
        })();
      `;

      // Add the script to the iframe document
      iframe.contentDocument.body.appendChild(script);

      // Add event listener for messages from the iframe
      window.addEventListener('message', this.handleIframeMessage);

    } catch (error) {
      this.logger.error('Error injecting selection scripts:', error);
    }
  }

  /**
   * Handles messages from the iframe
   */
  private handleIframeMessage = (event: MessageEvent): void => {
    // Check if the message is from our iframe
    if (event.data && event.data.type === 'elementSelected') {

      // Store the selected element data
      this.selectedElementTagName = event.data.data.tagName;
      this.selectedElementId = event.data.data.id;
      this.selectedElementHTML = event.data.data.html;
      this.selectedElementCSS = JSON.stringify(event.data.data.css, null, 2);
      this.selectedElementPath = event.data.data.path;

      // Add a message to the chat window with the selected element
      this.addSelectedElementToChat(event.data.data);

      // Turn off selection mode
      this.isElementSelectionMode = false;
      this.cleanupSelectionMode();

      // Force change detection
      this.ngZone.run(() => {
        this.cdr.detectChanges();
      });
    }
  };

  /**
   * Adds the selected element to the chat window
   */
  private addSelectedElementToChat(elementData: any): void {
    // Create a simplified HTML representation for display
    const displayHTML = this.formatHTMLForDisplay(elementData.html);

    // Create a message with the selected element
    const message = `## Selected Element: \`<${elementData.tagName}>\`

\`\`\`html
${displayHTML}
\`\`\`

**Element Path:** \`${elementData.path}\`

Would you like to modify this element? Please describe the changes you'd like to make.`;

    // Add the message to the chat
    this.lightMessages.push({
      text: message,
      from: 'ai',
      theme: 'light',
    });

    // Force change detection
    this.cdr.detectChanges();
  }

  /**
   * Formats HTML for display in the chat window
   */
  private formatHTMLForDisplay(html: string): string {
    // Limit the HTML to a reasonable length
    if (html.length > 500) {
      html = html.substring(0, 500) + '...';
    }

    // Add indentation for better readability
    return html
      .replace(/></g, '>\n<')
      .replace(/(<[^\/].*?>)/g, '$1')
      .replace(/(<\/.*?>)/g, '$1');
  }

  /**
   * Cleans up selection mode
   */
  private cleanupSelectionMode(): void {
    try {
      // Get the iframe element
      const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
      if (!iframe || !iframe.contentWindow) {
        return;
      }

      // For cross-origin iframes, we can't directly access properties
      // Instead, send a message to the iframe to clean up
      try {
        // First try direct access (same-origin case)
        if (iframe.contentWindow && 'function' === typeof (iframe.contentWindow as any)._elementSelectionCleanup) {
          (iframe.contentWindow as any)._elementSelectionCleanup();
        } else {
          // If direct access fails or isn't available, use postMessage
          iframe.contentWindow.postMessage({ type: 'cleanupElementSelection' }, '*');
          this.logger.debug('Sent cleanup message to iframe');
        }
      } catch (crossOriginError) {
        // If we get a cross-origin error, use postMessage as fallback
        this.logger.info('Using postMessage fallback for cross-origin iframe cleanup');
        iframe.contentWindow.postMessage({ type: 'cleanupElementSelection' }, '*');
      }

      // Remove the event listener
      window.removeEventListener('message', this.handleIframeMessage);

      this.logger.info('Selection mode cleaned up');
    } catch (error) {
      this.logger.error('Error cleaning up selection mode:', error);
    }
  }

  /**
   * Clears the selected element
   */
  private clearSelectedElement(): void {
    this.selectedElementTagName = null;
    this.selectedElementId = null;
    this.selectedElementHTML = null;
    this.selectedElementCSS = null;
    this.selectedElementPath = null;
    this.selectedElement = null;
  }

  /**
   * Handles edit button click to toggle element selection mode
   */
  onEditButtonClick(): void {
    // Toggle element selection mode
    this.toggleElementSelectionMode();
  }

  /**
   * Handles sending a message with the selected element modification request
   * @param message The user's message describing the desired modifications
   */
  sendElementModificationRequest(message: string): void {
    if (!this.selectedElementHTML || !this.selectedElementPath) {
      this.logger.error('No element selected for modification');
      return;
    }

    // Get user signature
    const userSignature = this.userSignatureService.getUserSignatureSync();
    this.logger.debug('Using user signature for element modification:', userSignature);

    // Create the payload for the API call
    const payload = {
      elementHtml: this.selectedElementHTML,
      elementCss: this.selectedElementCSS,
      elementPath: this.selectedElementPath,
      userMessage: message,
      projectId: this.projectId,
      jobId: this.jobId,
      userSignature: userSignature
    };

    this.logger.debug('Sending element modification request:', payload);

    // Add user message to chat
    this.lightMessages.push({
      text: message,
      from: 'user',
      theme: 'light',
    });

    // Add a loading message from AI
    this.lightMessages.push({
      text: "I'm processing your request to modify the selected element. This may take a moment...",
      from: 'ai',
      theme: 'light',
    });

    // Make the API call to modify the element
    this.codeGenerationService.modifyElement(payload).subscribe({
      next: response => {
        // Update the last AI message with the response
        const lastAiMessage = this.lightMessages.find(msg => msg.from === 'ai');
        if (lastAiMessage) {
          lastAiMessage.text = `I've updated the element based on your request. Here's what I did:

\`\`\`html
${this.formatHTMLForDisplay(response.modifiedHtml || '')}
\`\`\`

The changes should be reflected in the preview shortly.`;
        }

        // Clear the selected element
        this.clearSelectedElement();

        // Force change detection
        this.cdr.detectChanges();
      },
      error: error => {
        this.logger.error('Error modifying element:', error);

        // Update the last AI message with the error
        const lastAiMessage = this.lightMessages.find(msg => msg.from === 'ai');
        if (lastAiMessage) {
          lastAiMessage.text = `I encountered an error while trying to modify the element. Please try again or select a different element.`;
        }

        // Force change detection
        this.cdr.detectChanges();
      },
    });
  }

  // Method to handle editor icon click (legacy method, kept for compatibility)
  onEditorIconClick() {
    // Toggle element selection mode
    this.toggleElementSelectionMode();
  }

  /**
   * Handle retry button click from error page
   * Restarts the stepper from where it left off and restarts polling
   */
  onRetryClick() {
    // Reset error state using BehaviorSubject
    this.previewError$.next(false);

    // Reset code generation complete flag to hide code tab and show loading state
    this.isCodeGenerationComplete = false;

    // Reset code tab enabled flag
    this.isCodeTabEnabled = false;

    // Ensure code tab is not active when retrying using BehaviorSubjects
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isLogsActive$.next(false);

    // Show toast notification
    this.toastService.info('Retrying code generation...');

    // If we have project ID and job ID, restart polling
    if (this.projectId && this.jobId) {
      // Get the current step from the polling service
      const currentStep = this.pollingService.getCurrentStep();
      // const lastProgressDescription = this.pollingService.getLastProgressDescription(); // Unused for now

      // Reset the polling service but keep the current step
      this.pollingService.resetLogsButKeepStep();

      // Start polling again with the same project and job IDs
      this.pollingService.startPolling(this.projectId, this.jobId, {
        taskType: 'code generation retry',
        initialInterval: 3000,
        maxInterval: 20000,
        backoffFactor: 1.5,
        startFromStep: currentStep // Pass the current step to resume from
      });

      this.updatePollingStatus(true);

      // Show loading animation using BehaviorSubjects
      this.isLoading$.next(true);
      this.isPreviewLoading$.next(true);

      // Switch to preview view
      this.togglePreviewView();
    } else {
      // If we don't have project ID and job ID, navigate to home
      this.navigateToHome();
    }
  }

  /**
   * Highlights an element in the iframe using a CSS selector
   * This method is kept for future use but is currently unused
   * @param selector CSS selector to find the element
   */
  // private highlightElementInIframe(selector: string) {
  //   const iframe = document.querySelector('.preview-frame') as HTMLIFrameElement;
  //   if (iframe?.contentDocument) {
  //     const element = iframe.contentDocument.querySelector(selector);
  //     if (element) {
  //       element.classList.add(this.lastSelectedElementClass);
  //     }
  //   }
  // }

  /**
   * Sorts files based on the selected technology (React, Angular, Vue.js)
   * @param files Array of file models to sort
   * @param technology The selected technology ('react', 'angular', 'vue')
   * @returns Sorted array of file models
   */
  private sortFilesByTechnology(files: FileModel[], technology: string): FileModel[] {
    if (!files || files.length === 0) {
      return files;
    }

    // Define file priority based on technology (reversed priority - higher number = higher priority)
    const filePriority: { [key: string]: { [key: string]: number } } = {
      react: {
        // Source files (highest priority)
        'src/components/': 100,
        'components/': 100,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/hooks/': 96,
        'hooks/': 96,
        'src/context/': 95,
        'context/': 95,
        'src/assets/': 94,
        'assets/': 94,
        'src/styles/': 93,
        'styles/': 93,
        'src/App.js': 92,
        'src/App.jsx': 92,
        'src/App.tsx': 92,
        'App.js': 92,
        'App.jsx': 92,
        'App.tsx': 92,
        'src/App.css': 91,
        'App.css': 91,
        'src/index.js': 90,
        'src/index.jsx': 90,
        'src/index.tsx': 90,
        'index.js': 90,
        'index.jsx': 90,
        'index.tsx': 90,
        'public/index.html': 89,
        'index.html': 89,
        'public/': 88,
        '.js': 80,
        '.jsx': 80,
        '.tsx': 80,
        '.css': 79,
        '.scss': 79,
        '.html': 78,

        // Configuration files (lowest priority)
        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
      angular: {
        // Source files (highest priority)
        'src/app/components/': 100,
        'components/': 100,
        'src/app/pages/': 99,
        'pages/': 99,
        'src/app/services/': 98,
        'services/': 98,
        'src/app/models/': 97,
        'models/': 97,
        'src/app/directives/': 96,
        'directives/': 96,
        'src/app/pipes/': 95,
        'pipes/': 95,
        'src/app/guards/': 94,
        'guards/': 94,
        'src/app/interceptors/': 93,
        'interceptors/': 93,
        'src/assets/': 92,
        'assets/': 92,
        'src/environments/': 91,
        'environments/': 91,
        'src/app/app.component.ts': 90,
        'app.component.ts': 90,
        'src/app/app.component.html': 89,
        'app.component.html': 89,
        'src/app/app.component.scss': 88,
        'src/app/app.component.css': 88,
        'app.component.scss': 88,
        'app.component.css': 88,
        'src/app/app-routing.module.ts': 87,
        'app-routing.module.ts': 87,
        'src/app/app.module.ts': 86,
        'app.module.ts': 86,
        'src/main.ts': 85,
        'main.ts': 85,
        'src/index.html': 84,
        'index.html': 84,
        '.ts': 80,
        '.html': 79,
        '.scss': 78,
        '.css': 78,

        // Configuration files (lowest priority)
        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
      vue: {
        // Source files (highest priority)
        'src/components/': 100,
        'components/': 100,
        'src/views/': 99,
        'views/': 99,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/assets/': 96,
        'assets/': 96,
        'src/styles/': 95,
        'styles/': 95,
        'src/store/index.js': 94,
        'src/store/index.ts': 94,
        'store/index.js': 94,
        'store/index.ts': 94,
        'src/router/index.js': 93,
        'src/router/index.ts': 93,
        'router/index.js': 93,
        'router/index.ts': 93,
        'src/App.vue': 92,
        'App.vue': 92,
        'src/main.js': 91,
        'src/main.ts': 91,
        'main.js': 91,
        'main.ts': 91,
        'public/index.html': 90,
        'index.html': 90,
        'public/': 89,
        '.vue': 80,
        '.js': 79,
        '.ts': 79,
        '.scss': 78,
        '.css': 78,
        '.html': 77,

        // Configuration files (lowest priority)
        'tsconfig.json': 20,
        'staticwebapp.config.json': 19,
        'package-lock.json': 18,
        'eslint.config.js': 17,
        'azure-pipelines.yml': 16,
        'angular.json': 15,
        '.npmrc': 14,
        '.hintrc': 13,
        '.editorconfig': 12,
        'projects/playground/': 11,
        'projects/play-comp-library/': 10,
        'projects/': 9,
        'Pipeline/play-library.yml': 8,
        'Pipeline/': 7,
        'node_modules/': 6,
        'dist/': 5,
        '.vscode/': 4,
        '.angular/': 3,
        '.gitignore': 2,
        'README.md': 1,
        'package.json': 0,
      },
    };

    // Normalize technology name
    const normalizedTech = technology.toLowerCase();
    const techKey = normalizedTech.includes('react')
      ? 'react'
      : normalizedTech.includes('angular')
        ? 'angular'
        : normalizedTech.includes('vue')
          ? 'vue'
          : 'angular';

    // Get the priority map for the selected technology
    const priorityMap = filePriority[techKey] || filePriority['angular']; // Default to Angular if not found

    // Sort the files based on priority
    const sortedFiles = [...files].sort((a, b) => {
      const aName = a.name.toLowerCase();
      const bName = b.name.toLowerCase();

      // Get priority for file A
      let aPriority = 1000; // Default high number for low priority
      let aNestedLevel = aName.split('/').length - 1; // Calculate nesting level

      for (const [pattern, priority] of Object.entries(priorityMap)) {
        if (pattern.endsWith('/')) {
          // Handle directory patterns
          if (aName.includes(pattern)) {
            aPriority = priority;
            break;
          }
        } else if (aName === pattern.toLowerCase()) {
          // Exact match
          aPriority = priority;
          break;
        } else if (aName.endsWith(pattern.toLowerCase())) {
          // Match file extension or end of path
          aPriority = priority;
          break;
        }
      }

      // Get priority for file B
      let bPriority = 1000; // Default high number for low priority
      let bNestedLevel = bName.split('/').length - 1; // Calculate nesting level

      for (const [pattern, priority] of Object.entries(priorityMap)) {
        if (pattern.endsWith('/')) {
          // Handle directory patterns
          if (bName.includes(pattern)) {
            bPriority = priority;
            break;
          }
        } else if (bName === pattern.toLowerCase()) {
          // Exact match
          bPriority = priority;
          break;
        } else if (bName.endsWith(pattern.toLowerCase())) {
          // Match file extension or end of path
          bPriority = priority;
          break;
        }
      }

      // Sort by priority first (higher number = higher priority)
      if (aPriority !== bPriority) {
        return bPriority - aPriority; // Reversed comparison for reversed priority
      }

      // If same priority, sort by nesting level (higher nesting level gets higher priority)
      if (aNestedLevel !== bNestedLevel) {
        return bNestedLevel - aNestedLevel; // Reverse order to prioritize deeper nesting
      }

      // If same nesting level, sort alphabetically
      return aName.localeCompare(bName);
    });

    // Process files to analyze nesting levels (for future use)
    // Commented out to avoid unused variable warning
    // sortedFiles.forEach(file => {
    //   const nestedLevel = file.name.split('/').length - 1;
    //   Process nesting level if needed in the future
    // });

    // Calculate nesting statistics (for future use)
    const nestingStats: Record<string, number> = {};
    sortedFiles.forEach(file => {
      const level = file.name.split('/').length - 1;
      const levelKey = level.toString();
      nestingStats[levelKey] = (nestingStats[levelKey] || 0) + 1;
    });

    // Process nesting statistics if needed in the future
    // Object.entries(nestingStats).forEach(([_level, _count]) => {
    //   // Process statistics if needed
    // });

    return sortedFiles;
  }

  /**
   * Initialize the chat with a default prompt if no prompt is available from the app state
   * This ensures that the user sees their original prompt in the chat window
   */
  private initializeDefaultPrompt(): void {
    // Only initialize if the lightMessages array is empty
    if (this.lightMessages.length === 0) {
      // Get the prompt from the app state if available
      this.appStateService.project$
        .subscribe(projectState => {
          if (projectState.prompt) {
            // Create a summary of user selections
            let userSelectionsSummary = '';

            // Add image information if available
            if (projectState.imageUrl) {
              userSelectionsSummary += '- **Image**:  You provided an image for reference\n';
            }

            // Add technology information if available
            if (projectState.technology) {
              userSelectionsSummary += `- **Technology**: ${projectState.technology.charAt(0).toUpperCase() + projectState.technology.slice(1)}\n`;
            }

            // Add design library information if available
            if (projectState.designLibrary) {
              userSelectionsSummary += `- **Design Library**: ${projectState.designLibrary.charAt(0).toUpperCase() + projectState.designLibrary.slice(1)}\n`;
            }

            // Add application type information if available
            if (projectState.application) {
              userSelectionsSummary += `- **Application Type**: ${projectState.application.charAt(0).toUpperCase() + projectState.application.slice(1)}\n`;
            }

            // Add type information if available
            if (projectState.type) {
              userSelectionsSummary += `- **Generation Type**: ${projectState.type}\n`;
            }

            // Add the user prompt to the chat messages with a comprehensive project overview
            this.lightMessages = [
              {
                text: projectState.prompt,
                from: 'user',
                theme: 'light',
                imageDataUri: projectState.imageDataUri || undefined
              },
//               {
//                 text: `# Project Overview
// ## Your Selections
// ${userSelectionsSummary.trim() ? userSelectionsSummary : '**No additional selections provided**'}.`,
//                 from: 'ai',
//                 theme: 'light',
//               },
            ];

            // Also store the image data URI for the chat window component
            if (projectState.imageDataUri) {
              this.selectedImageDataUri = projectState.imageDataUri;
            }
          } else {
            // If no prompt is available, add a default message
            this.lightMessages = [
              {
                text: "Welcome to the code generation experience. I'll help you create code based on your requirements.",
                from: 'ai',
                theme: 'light',
              },
            ];
          }
        })
        .unsubscribe(); // Unsubscribe immediately since we only need the current value
    }
  }

  /**
   * Starts streaming logs with enhanced typewriter effect using TypewriterService
   * @param allLogs Array of all log messages from the API
   */
  private startLogStreaming(allLogs: string[]): void {
    // Clear any existing timer
    if (this.logStreamTimer) {
      clearInterval(this.logStreamTimer);
    }

    // If no logs, don't start streaming
    if (!allLogs || allLogs.length === 0) {
      this.isStreamingLogs = false;
      return;
    }

    // Initialize streaming state if this is the first time
    if (!this.hasLogs) {
      this.hasLogs = true;
    }

    // Filter out logs we've already processed to avoid duplicates
    const newLogs = this.filterNewLogs(allLogs);

    if (newLogs.length === 0) {
      this.isStreamingLogs = false;
      return;
    }

    this.logger.debug(`Processing ${newLogs.length} new logs out of ${allLogs.length} total logs`);

    // Process and format only the new logs
    const processedLogs = this.processLogs(newLogs);

    if (processedLogs.length > 0) {
      // Set streaming state to true only if we have new logs to display
      this.isStreamingLogs = true;

      // Append the new processed logs to our existing formatted logs array
      this.formattedLogMessages = [...this.formattedLogMessages, ...processedLogs];

      // Keep the original logs for compatibility
      this.logMessages = [...this.logMessages, ...newLogs];

      // Use enhanced typewriter service for new logs
      this.startEnhancedLogTypewriter(processedLogs);
    }
  }

  /**
   * Start enhanced typewriter animation for logs using TypewriterService
   * @param newLogs Array of new processed logs to animate
   */
  private startEnhancedLogTypewriter(newLogs: any[]): void {
    // Create or get batch typewriter instance for logs
    if (!this.logTypewriterInstance) {
      this.logTypewriterInstance = this.typewriterService.createBatch({
        contentType: ContentType.TEXT,
        typingSpeed: 40,
        pauseBeforeTyping: 200,
        showCursor: false,
        onStateChange: (state) => {
          this.isTypingLog = state === TypewriterState.TYPING;
          this.isStreamingLogs = state === TypewriterState.TYPING;
          this.cdr.detectChanges();
        }
      });

      // Subscribe to text updates
      const textObservable = this.typewriterService.getText$(this.logTypewriterInstance);
      if (textObservable) {
        textObservable.subscribe((text: string) => {
          this.updateLogVisibility(text);
        });
      }
    }

    // Add new logs to the typewriter service
    newLogs.forEach((log) => {
      const logText = log.type === 'code' ?
        `[${log.timestamp}] Generated code file: ${log.path}` :
        `[${log.timestamp}] ${log.content}`;

      if (this.logTypewriterInstance) {
        this.typewriterService.addText(this.logTypewriterInstance, logText);
      }
    });
  }

  /**
   * Update log visibility based on typewriter progress
   * @param accumulatedText The accumulated text from typewriter
   */
  private updateLogVisibility(accumulatedText: string): void {
    const lines = accumulatedText.split('\n').filter(line => line.trim());

    // Update visible content for each log based on typewriter progress
    lines.forEach((line, index) => {
      if (this.formattedLogMessages[index]) {
        this.formattedLogMessages[index].visibleContent = line;

        // Update current typing index for template
        this.currentLogIndex = index;
      }
    });

    this.cdr.detectChanges();
  }

  /**
   * Start typewriter animation for artifact content
   * @param artifact The artifact to animate
   */
  private startArtifactTypewriter(artifact: any): void {
    // Only animate markdown content
    if (artifact.type !== 'markdown' || !artifact.content) {
      return;
    }

    const artifactKey = artifact.name;

    // Stop any existing typewriter for this artifact
    const existingInstanceId = this.artifactTypewriterInstances.get(artifactKey);
    if (existingInstanceId) {
      this.typewriterService.stop(existingInstanceId);
    }

    // Create new typewriter instance
    const instanceId = this.typewriterService.createInstance([artifact.content], {
      mode: TypewriterMode.SINGLE,
      contentType: ContentType.MARKDOWN,
      typingSpeed: 30,
      showCursor: false,
      onComplete: () => {
        this.cdr.detectChanges();
      }
    });

    this.artifactTypewriterInstances.set(artifactKey, instanceId);

    // Subscribe to text updates
    const textObservable = this.typewriterService.getText$(instanceId);
    if (textObservable) {
      textObservable.subscribe((text: string) => {
        this.artifactVisibleContent[artifactKey] = text;
        this.cdr.detectChanges();
      });
    }

    // Start the animation
    this.typewriterService.start(instanceId);
  }

  /**
   * Filter out logs that have already been processed to avoid duplicates
   * @param allLogs All logs from the API
   * @returns Only the new logs that haven't been processed yet
   */
  private filterNewLogs(allLogs: string[]): string[] {
    return allLogs.filter(log => {
      // Create a simple hash of the log to use as a unique identifier
      const logHash = this.createLogHash(log);

      // If we've already processed this log, skip it
      if (this.processedLogHashes.has(logHash)) {
        return false;
      }

      // Otherwise, add it to our set of processed logs and include it
      this.processedLogHashes.add(logHash);
      return true;
    });
  }

  /**
   * Create a simple hash of a log message to use as a unique identifier
   * @param log The log message
   * @returns A string hash
   */
  private createLogHash(log: string): string {
    // For simplicity, we'll use the first 100 characters of the log as a hash
    // In a production environment, you might want to use a more robust hashing algorithm
    return log.substring(0, 100);
  }

  /**
   * Process logs to extract code blocks and format them properly
   * @param logs Array of raw log messages
   * @returns Array of formatted log objects
   */
  private processLogs(logs: string[]): any[] {
    const processedLogs: any[] = [];
    // Track logs that have been processed as JSON to avoid duplicates
    const processedJsonLogs = new Set<string>();

    this.logger.debug('Processing logs:', logs.length);

    logs.forEach(log => {
      // Skip empty logs
      if (!log || log.trim() === '') {
        return;
      }

      // Extract the log content for state change detection
      const logContent = this.extractLogContent(log);

      // Check if this is a log containing code (key-value pair format)
      // Special handling for logs that contain "Generated code:" marker
      if (log.includes('Generated code:')) {
        try {
          // Extract the JSON part after "Generated code:"
          const jsonStartIndex = log.indexOf('Generated code:') + 'Generated code:'.length;
          const jsonPart = log.substring(jsonStartIndex).trim();
          this.logger.debug('Found log with Generated code marker, JSON length:', jsonPart.length);

          if (jsonPart) {
            const codeData = JSON.parse(jsonPart);
            this.logger.debug('Successfully parsed code data from Generated code log');

            // Mark this log as processed to avoid showing the raw JSON
            processedJsonLogs.add(log);

            // Process the code data directly
            if (typeof codeData === 'object' && codeData !== null) {
              if (Array.isArray(codeData)) {
                // Handle array of files
                codeData.forEach(item => {
                  if (item && item.fileName && item.content) {
                    const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                    processedLogs.push({
                      id: codeId,
                      type: 'code',
                      timestamp: this.extractTimestamp(log),
                      path: item.fileName,
                      content: item.content,
                      contentLines: item.content.split('\n').length,
                      contentSize: item.content.length,
                      rawLog: log,
                    });

                    // Auto-expand code logs
                    this.expandedCodeLogs.add(codeId);
                  }
                });
                return;
              } else {
                // Handle object with file paths as keys
                Object.entries(codeData).forEach(([path, content]) => {
                  if (path && content) {
                    const formattedContent =
                      typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                    const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                    processedLogs.push({
                      id: codeId,
                      type: 'code',
                      timestamp: this.extractTimestamp(log),
                      path: path,
                      content: formattedContent,
                      contentLines: formattedContent.split('\n').length,
                      contentSize: formattedContent.length,
                      rawLog: log,
                    });

                    // Auto-expand code logs
                    this.expandedCodeLogs.add(codeId);
                  }
                });
                return;
              }
            }
          }
        } catch (e) {
          this.logger.error('Error processing Generated code log:', e);
        }
      }

      // Regular JSON detection in logs
      if (log.includes('{') && log.includes('}')) {
        try {
          // Try to extract JSON from the log
          const jsonStartIndex = log.indexOf('{');
          const jsonEndIndex = log.lastIndexOf('}') + 1;

          if (jsonStartIndex !== -1 && jsonEndIndex > jsonStartIndex) {
            const jsonPart = log.substring(jsonStartIndex, jsonEndIndex);
            this.logger.debug('Found potential JSON in log:', jsonPart.substring(0, 100) + '...');

            const codeData = JSON.parse(jsonPart);
            this.logger.debug(
              'Parsed code data type:',
              typeof codeData,
              Array.isArray(codeData) ? 'array' : ''
            );

            // Mark this log as processed to avoid showing the raw JSON
            processedJsonLogs.add(log);

            // Special handling for logs with filesToGenerate array
            if (codeData && typeof codeData === 'object' && codeData.filesToGenerate && Array.isArray(codeData.filesToGenerate)) {
              this.logger.debug('Found filesToGenerate array in log:', codeData.filesToGenerate);

              // Format the files list as a nicely formatted string with file type icons
              const formattedFiles = codeData.filesToGenerate.map((file: string) => {
                // Add file extension icon based on file type
                let icon = '📄';
                if (file.endsWith('.ts') || file.endsWith('.tsx')) icon = '📘';
                else if (file.endsWith('.js') || file.endsWith('.jsx')) icon = '📙';
                else if (file.endsWith('.css')) icon = '🎨';
                else if (file.endsWith('.html')) icon = '🌐';
                else if (file.endsWith('.json')) icon = '📋';

                return `- ${icon} ${file}`;
              });

              const formattedContent = `# Files to Generate\n\n${formattedFiles.join('\n')}`;

              // Skip if formatted content is empty
              if (!formattedContent || formattedContent.trim() === '') return;

              // Add the code log with a unique ID
              const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
              processedLogs.push({
                id: codeId,
                type: 'code',
                timestamp: this.extractTimestamp(log),
                path: 'Project Structure',
                content: formattedContent,
                contentLines: formattedContent.split('\n').length,
                contentSize: formattedContent.length,
                rawLog: log,
              });

              // Auto-expand this code log
              this.expandedCodeLogs.add(codeId);

              // Also add a regular log message to indicate what's happening
              processedLogs.push({
                type: 'info',
                timestamp: this.extractTimestamp(log),
                content: `Preparing to generate ${codeData.filesToGenerate.length} files`,
                rawLog: log,
              });

              return;
            }

            // Special handling for logs with message and data fields where data is a JSON string containing code
            if (codeData && typeof codeData === 'object' && codeData.message && codeData.data) {
              this.logger.debug('Found log with message and data fields:', codeData.message);

              // Check if data is a string that might contain code
              if (typeof codeData.data === 'string') {
                // Handle the data field which might be a JSON string
                let dataString = codeData.data;
                let dataJson = null;

                // First try to parse it directly
                try {
                  dataJson = JSON.parse(dataString);
                } catch (directParseError) {
                  // If direct parsing fails and the string contains escaped characters, try to unescape it
                  if (dataString.includes('\\\"') || dataString.includes('\\\\')) {
                    try {
                      // Replace escaped quotes and backslashes
                      dataString = dataString.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
                      dataJson = JSON.parse(dataString);
                    } catch (unescapeError) {
                    }
                  }
                }

                // If we successfully parsed the data field as JSON
                if (dataJson) {
                  // Handle different formats of the data field
                  if (typeof dataJson === 'object' && !Array.isArray(dataJson)) {
                    // Object with file paths as keys
                    // Process each file path and content
                    Object.entries(dataJson).forEach(([path, content]) => {
                      if (path && content) {
                        const formattedContent =
                          typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                        // Skip if formatted content is empty
                        if (!formattedContent || formattedContent.trim() === '') return;

                        const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                        processedLogs.push({
                          id: codeId,
                          type: 'code',
                          timestamp: this.extractTimestamp(log),
                          path: path,
                          content: formattedContent,
                          contentLines: formattedContent.split('\n').length,
                          contentSize: formattedContent.length,
                          rawLog: log,
                        });

                        // Auto-expand code logs
                        this.expandedCodeLogs.add(codeId);
                      }
                    });
                  } else if (Array.isArray(dataJson)) {
                    dataJson.forEach(item => {
                      if (item && typeof item === 'object') {
                        const path = item.fileName || item.path || item.name || 'unknown.file';
                        const content = item.content || '';

                        if (path && content) {
                          const formattedContent =
                            typeof content === 'string'
                              ? content
                              : JSON.stringify(content, null, 2);

                          // Skip if formatted content is empty
                          if (!formattedContent || formattedContent.trim() === '') return;

                          const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                          processedLogs.push({
                            id: codeId,
                            type: 'code',
                            timestamp: this.extractTimestamp(log),
                            path: path,
                            content: formattedContent,
                            contentLines: formattedContent.split('\n').length,
                            contentSize: formattedContent.length,
                            rawLog: log,
                          });

                          // Auto-expand code logs
                          this.expandedCodeLogs.add(codeId);
                        }
                      }
                    });
                  }

                  // Also add the original message as an info log
                  processedLogs.push({
                    type: 'info',
                    timestamp: this.extractTimestamp(log),
                    content: codeData.message,
                    rawLog: log,
                  });

                  return;
                }
              }
            }

            // Check if this is a key-value pair format (path: content)
            if (typeof codeData === 'object' && codeData !== null) {
              // Handle object format with key-value pairs
              if (!Array.isArray(codeData)) {
                // Process each key-value pair as a separate code block
                Object.entries(codeData).forEach(([path, content]) => {

                  // Skip if content is empty
                  if (!content || (typeof content === 'string' && content.trim() === '')) {
                    return;
                  }

                  // Skip empty content
                  if (!content) return;

                  // Format the content properly
                  const formattedContent =
                    typeof content === 'string' ? content : JSON.stringify(content, null, 2);

                  // Skip if formatted content is empty or just whitespace
                  if (!formattedContent || formattedContent.trim() === '') return;

                  // Calculate content size for proper capsule sizing
                  const contentLines = formattedContent.split('\n').length;
                  const contentSize = formattedContent.length;

                  // Add the code log with a unique ID
                  const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  processedLogs.push({
                    id: codeId,
                    type: 'code',
                    timestamp: this.extractTimestamp(log),
                    path: path || 'unknown.file',
                    content: formattedContent,
                    contentLines: contentLines,
                    contentSize: contentSize,
                    rawLog: log,
                  });

                  // Auto-expand code logs
                  this.expandedCodeLogs.add(codeId);
                });
                return;
              }
              // Handle array format with fileName and content properties
              else if (Array.isArray(codeData) && codeData.length > 0) {
                codeData.forEach(item => {
                  // Skip if content is empty
                  if (
                    !item.content ||
                    (typeof item.content === 'string' && item.content.trim() === '')
                  ) {
                    return;
                  }

                  // Format the content properly
                  const formattedContent =
                    typeof item.content === 'string'
                      ? item.content
                      : JSON.stringify(item.content, null, 2);

                  // Skip if formatted content is empty
                  if (!formattedContent || formattedContent.trim() === '') return;

                  // Add the code log with a unique ID
                  const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  processedLogs.push({
                    id: codeId,
                    type: 'code',
                    timestamp: this.extractTimestamp(log),
                    path: item.fileName || item.path || 'unknown.file',
                    content: formattedContent,
                    contentLines: formattedContent.split('\n').length,
                    contentSize: formattedContent.length,
                    rawLog: log,
                  });

                  // Auto-expand code logs
                  this.expandedCodeLogs.add(codeId);
                });
                return;
              }
              // Handle single file format with fileName and content properties
              else if (
                typeof codeData === 'object' &&
                'fileName' in codeData &&
                'content' in codeData
              ) {
                // Skip if content is empty
                if (
                  !codeData.content ||
                  (typeof codeData.content === 'string' && codeData.content.trim() === '')
                ) {
                  return;
                }

                // Format the content properly
                const formattedContent =
                  typeof codeData.content === 'string'
                    ? codeData.content
                    : JSON.stringify(codeData.content, null, 2);

                // Skip if formatted content is empty or just whitespace
                if (!formattedContent || formattedContent.trim() === '') return;

                // Calculate content size for proper capsule sizing
                const contentLines = formattedContent.split('\n').length;
                const contentSize = formattedContent.length;

                // Add the code log with a unique ID
                const codeId = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                processedLogs.push({
                  id: codeId,
                  type: 'code',
                  timestamp: this.extractTimestamp(log),
                  path: codeData.fileName || 'unknown.file',
                  content: formattedContent,
                  contentLines: contentLines,
                  contentSize: contentSize,
                  rawLog: log,
                });

                // Auto-expand code logs
                this.expandedCodeLogs.add(codeId);
                return;
              }
            }
          }
        } catch (e) {
          this.logger.error('Error parsing code from log:', e);
        }
      }

      // Check for error logs - always show errors
      if (log.includes('ERROR')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          type: 'error',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // Check for warning logs - always show warnings
      if (log.includes('WARN')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          type: 'warning',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // Check for progress description logs - only show if there's a state change
      if (log.includes('Progress Description')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        // Extract the actual progress description
        const progressDesc = logContent.replace('Progress Description Updated: ', '').trim();

        // Check if this is a new state
        if (progressDesc !== this.lastProgressState) {
          this.lastProgressState = progressDesc;

          processedLogs.push({
            type: 'progress-description',
            timestamp: this.extractTimestamp(log),
            content: logContent,
            rawLog: log,
          });
        }
        return;
      }

      // Check for status change logs - always show status changes
      if (log.includes('Status changed to:')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        processedLogs.push({
          type: 'status-change',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // For debug logs, only show if they contain useful information
      if (log.includes('DEBUG')) {
        // Skip if content is empty
        if (!logContent || logContent.trim() === '') {
          return;
        }

        // Skip raw response logs entirely
        if (logContent.includes('Raw response:')) {
          return;
        }

        // Only include debug logs that have actual content
        processedLogs.push({
          type: 'debug',
          timestamp: this.extractTimestamp(log),
          content: logContent,
          rawLog: log,
        });
        return;
      }

      // For info logs, only include if they have meaningful content and haven't been processed as JSON
      if (!logContent || logContent.trim() === '' || processedJsonLogs.has(log)) {
        return;
      }

      // Skip logs that contain raw JSON data that we've already processed
      if ((log.includes('{') && log.includes('}')) || (log.includes('[') && log.includes(']'))) {
        // Check if this log contains JSON that we might have already processed
        // This helps filter out the raw JSON logs when we've already processed them as structured data
        const jsonStartIndex = log.indexOf('{');
        const jsonEndIndex = log.lastIndexOf('}') + 1;

        if (jsonStartIndex !== -1 && jsonEndIndex > jsonStartIndex) {
          try {
            // Try to parse the JSON to see if it's valid
            const jsonPart = log.substring(jsonStartIndex, jsonEndIndex);
            JSON.parse(jsonPart);

            // If we get here, it's valid JSON - check if we've already processed a similar log
            // Skip this log if it looks like raw JSON data
            if (jsonPart.includes('"data"') || jsonPart.includes('"filesToGenerate"') ||
                jsonPart.includes('"fileName"') || jsonPart.includes('"content"')) {
              return;
            }
          } catch (e) {
            // Not valid JSON, continue processing as a normal log
          }
        }
      }

      // Default to info log
      processedLogs.push({
        type: 'info',
        timestamp: this.extractTimestamp(log),
        content: logContent,
        rawLog: log,
      });
    });

    // Log the number of processed logs by type
    const logTypes = processedLogs.reduce((acc, log) => {
      acc[log.type] = (acc[log.type] || 0) + 1;
      return acc;
    }, {});

    this.logger.debug('Processed logs by type:', logTypes);
    this.logger.debug('Code logs:', processedLogs.filter(log => log.type === 'code').length);

    return processedLogs;
  }

  /**
   * Extract timestamp from a log message
   * @param log The log message
   * @returns The extracted timestamp
   */
  private extractTimestamp(log: string): string {
    // Format: "HH:MM:SS.mmm - LEVEL - Message"
    const parts = log.split(' - ');
    if (parts.length >= 1) {
      return parts[0];
    }
    return '';
  }

  /**
   * Extract the content part from a log message
   * @param log The log message
   * @returns The extracted content
   */
  private extractLogContent(log: string): string {
    // Format: "HH:MM:SS.mmm - LEVEL - Message"
    const parts = log.split(' - ');
    if (parts.length >= 3) {
      return parts.slice(2).join(' - ');
    }
    return log;
  }

  /**
   * Start the letter-by-letter typing animation for logs
   * @param startIndex Optional index to start typing from (for appending new logs)
   */
  private startLetterByLetterTyping(startIndex: number = 0): void {
    // Set initial state
    this.isTypingLog = true;
    this.isStreamingLogs = true;
    this.currentLogIndex = startIndex;
    this.currentCharIndex = 0;

    // Initialize visibleContent property for each log if it doesn't exist
    for (let i = startIndex; i < this.formattedLogMessages.length; i++) {
      if (!this.formattedLogMessages[i].visibleContent) {
        this.formattedLogMessages[i].visibleContent = '';
      }
    }

    // Create a deep copy of the formatted logs to avoid modifying the original array
    // For existing logs, preserve their visible content
    const visibleLogs = this.formattedLogMessages.map((log, index) => {
      if (index < startIndex) {
        // For logs we've already processed, keep their visible content
        return {
          ...log,
          visibleContent: log.visibleContent || log.content || '',
        };
      } else {
        // For new logs, start with empty visible content
        return {
          ...log,
          visibleContent: '',
        };
      }
    });

    // For code logs, ensure they have IDs and are expanded by default
    visibleLogs.forEach((log, index) => {
      if (log.type === 'code' && index >= startIndex) {
        // For code logs, we'll use the typewriter effect with a slightly slower speed
        // to make it more visible
        log.visibleContent = ''; // Start with empty content for animation

        // Ensure the log has an ID
        if (!log.id) {
          log.id = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        }

        // Calculate appropriate max-height based on content size
        // This ensures the capsule expands enough to show all content
        if (log.contentLines) {
          // Set a custom data attribute for line count to use in CSS
          log.maxHeight = Math.min(Math.max(log.contentLines * 20, 100), 800); // 20px per line, min 100px, max 800px
        } else {
          // Default max height if we don't have line count
          log.maxHeight = 500;
        }

        // Auto-expand ALL code blocks to show the typewriting effect
        this.expandedCodeLogs.add(log.id);
      }
    });

    // Update the formatted logs array with the initial state
    this.formattedLogMessages = [...visibleLogs];

    // Much slower typing speeds for a highly visible typewriter effect
    // Very slow typing creates a dramatic character-by-character an  imation
    const baseTypingSpeed = 80; // 80ms per character for regular logs (much slower for high visibility)
    const codeTypingSpeed = 60; // 60ms per character for code logs (slightly faster than regular logs but still very visible)

    // Use requestAnimationFrame for smoother animation instead of setInterval
    const animateTyping = () => {
      // Call the typing step method which handles all the logic
      this.typingStep();

      // If we've processed all logs, stop the animation
      if (this.currentLogIndex >= this.formattedLogMessages.length) {
        this.isTypingLog = false;
        this.isStreamingLogs = false;
        return; // Stop the animation loop
      }

      // Determine the delay based on the current log type
      const delay = this.formattedLogMessages[this.currentLogIndex]?.type === 'code'
        ? codeTypingSpeed
        : baseTypingSpeed;

      // Schedule the next frame with the appropriate delay
      setTimeout(() => {
        requestAnimationFrame(animateTyping);
      }, delay);
    };

    // Start the animation loop
    requestAnimationFrame(animateTyping);
  }

  /**
   * Single step of the typing animation, extracted to a separate method
   * so it can be called with different intervals
   */
  private typingStep(): void {
    // If we've processed all logs, stop the animation
    if (this.currentLogIndex >= this.formattedLogMessages.length) {
      if (this.logStreamTimer) {
        clearInterval(this.logStreamTimer);
      }
      this.isTypingLog = false;
      this.isStreamingLogs = false;
      this.cdr.detectChanges(); // Ensure UI is updated
      return;
    }

    // Get the current log we're typing
    const currentLog = this.formattedLogMessages[this.currentLogIndex];
    const currentContent = currentLog.content || '';

    // Ensure visibleContent property exists
    if (!currentLog.visibleContent) {
      currentLog.visibleContent = '';
    }

    // If we're done typing this log, move to the next one
    if (this.currentCharIndex >= currentContent.length) {
      // For code logs, ensure the code capsule is expanded
      if (currentLog.type === 'code' && currentLog.id) {
        // Make sure the code capsule is fully expanded
        if (!this.expandedCodeLogs.has(currentLog.id)) {
          this.expandedCodeLogs.add(currentLog.id);
        }
      }

      // Add a small pause between logs for better readability
      // This creates a more natural typing effect
      setTimeout(() => {
        // Move to the next log
        this.currentLogIndex++;
        this.currentCharIndex = 0;

        // Auto-scroll to the bottom of the logs container
        this.scrollLogsToBottom();

        // Force change detection to update the UI
        this.cdr.detectChanges();
      }, 200); // Increased pause between logs for better readability

      return;
    }

    // Add the next character(s) to the visible content
    // Enhanced character-by-character animation

    // Determine how many characters to add at once based on log type and content
    let charsToAdd = 1; // Default is one character at a time

    if (currentLog.type === 'code') {
      // For code logs, we use a variable typing speed based on content type
      // This creates a more realistic coding effect

      // Get the current character and next few characters
      const currentChar = currentContent[this.currentCharIndex];
      const nextChars = currentContent.substring(this.currentCharIndex, this.currentCharIndex + 5);

      // Type faster through whitespace and common patterns
      if (currentChar === ' ' || currentChar === '\n' || currentChar === '\t') {
        charsToAdd = 1; // Type spaces and newlines at normal speed
      } else if (nextChars.match(/[{}\[\]()<>]/)) {
        charsToAdd = 1; // Type brackets one at a time for emphasis
      } else if (nextChars.match(/[a-zA-Z0-9_]+/)) {
        // Type identifiers (variable names, etc.) faster
        const identifierMatch = currentContent.substring(this.currentCharIndex).match(/^[a-zA-Z0-9_]+/);
        if (identifierMatch && identifierMatch[0].length > 3) {
          // For longer identifiers, type faster
          charsToAdd = Math.min(3, identifierMatch[0].length);
        } else {
          charsToAdd = 1;
        }
      } else if (nextChars.match(/["'`].*?["'`]/)) {
        // Type string literals slightly faster
        charsToAdd = 2;
      } else {
        // Default for code is slightly faster than regular logs
        charsToAdd = 2;
      }
    } else {
      // For regular logs, type 1-2 characters at a time
      // This creates a smoother, more natural typing effect
      charsToAdd = 1;

      // Type faster through spaces and punctuation
      const currentChar = currentContent[this.currentCharIndex];
      if (currentChar === ' ' || currentChar === '.' || currentChar === ',' || currentChar === ':') {
        charsToAdd = 2;
      }
    }

    // Limit chars to add to remaining content length
    charsToAdd = Math.min(charsToAdd, currentContent.length - this.currentCharIndex);

    // Update the visible content with the new character
    this.formattedLogMessages[this.currentLogIndex].visibleContent = currentContent.substring(
      0,
      this.currentCharIndex + charsToAdd
    );

    // Force change detection for the current log using Angular's change detection
    this.cdr.detectChanges();

    // Increment the character index by exactly one character
    this.currentCharIndex += charsToAdd;

    // Auto-scroll to the bottom of the logs container
    this.scrollLogsToBottom();
  }

  /**
   * Scrolls the logs container to the bottom to show the latest logs
   */
  private scrollLogsToBottom(): void {
    setTimeout(() => {
      const logsContainer = document.querySelector('.logs-content');
      if (logsContainer) {
        logsContainer.scrollTop = logsContainer.scrollHeight;
      }
    }, 0);
  }

  /**
   * Adds a log update message to the chat window
   * This method is kept for backward compatibility but is no longer used
   * @param logs Array of log messages
   */
  private addLogUpdateToChatWindow(_logs: string[]): void {
    // We don't want to show logs in the chat window anymore
    // Just make sure the logs tab is enabled
    this.isLogsTabEnabled = true;

    // We no longer process logs in the chat window
  }

  /**
   * Formats the LAYOUT_ANALYZED description to be more user-friendly
   * @param description The progress description containing LAYOUT_ANALYZED
   * @returns A formatted description with better structure
   */
  private formatLayoutAnalyzedDescription(description: string): string {
    if (!description.includes('LAYOUT_ANALYZED')) {
      return description;
    }

    // Extract the file list from the description
    const layoutMatch = description.match(/LAYOUT_ANALYZED\s+(.*?)(?:\n|$)/);

    if (layoutMatch && layoutMatch[1]) {
      const fileList = layoutMatch[1].trim();

      // Split the file list by commas or spaces
      let files: string[] = [];

      // Check if the file list contains commas
      if (fileList.includes(',')) {
        files = fileList.split(',').map(file => file.trim());
      }
      // Check if it's a space-separated list
      else if (fileList.includes(' ')) {
        files = fileList.split(/\s+/).filter(file => file.trim() !== '');
      }
      // If it's a single file
      else {
        files = [fileList];
      }

      // Filter out any empty strings
      files = files.filter(file => file.trim() !== '');

      // Create a formatted file list with bullet points
      const formattedFileList = files
        .map(file => {
          // Add file extension icon based on file type
          let icon = '📄';
          if (file.endsWith('.ts') || file.endsWith('.tsx')) icon = '📘';
          else if (file.endsWith('.js') || file.endsWith('.jsx')) icon = '📙';
          else if (file.endsWith('.css')) icon = '🎨';
          else if (file.endsWith('.html')) icon = '🌐';
          else if (file.endsWith('.json')) icon = '📋';

          return `- ${icon} ${file}`;
        })
        .join('\n');

      // Replace the original file list with the formatted one
      return description.replace(
        /LAYOUT_ANALYZED\s+(.*?)(?:\n|$)/,
        `### Project Structure Analysis\n\nThe following files will be generated:\n\n${formattedFileList}\n\n`
      );
    }

    return description;
  }

  /**
   * Adds a progress description to the chat window with a typewriter effect
   * @param description The progress description to add
   */
  // private addProgressDescriptionToChat(description: string): void {
  //   // Store the progress description for the stepper component
  //   // but don't append it to the AI message text

  //   // Just update the lastProgressDescription property which is bound to the stepper
  //   this.lastProgressDescription = description;

  //   // Make sure we have an AI message with hasSteps=true for the stepper
  //   let stepperMessage = this.lightMessages.find(msg => msg.hasSteps);

  //   // If no stepper message exists and we're polling, create one
  //   if (!stepperMessage && this.isPolling) {
  //     stepperMessage = {
  //       text: 'Processing your request...',
  //       from: 'ai' as 'ai',
  //       theme: 'light' as 'light',
  //       hasSteps: true
  //     };
  //     // this.lightMessages.push(stepperMessage);
  //   }

  //   // Force change detection
  //   this.cdr.detectChanges();

  //   // Auto-scroll the chat window
  //   this.scrollChatToBottom();



  /**
   * Track function for ngFor to improve rendering performance
   * This helps Angular identify which items have changed
   * @param index The index of the log in the array
   * @param log The log item
   * @returns A unique identifier for the log
   */
  // trackByLogIndex(index: number, log: any): string {
  //   // Use a combination of index and timestamp for uniqueness
  //   return `${index}-${log.timestamp}-${log.type}`;
  // }

  /**
   * Format code for display in the logs screen
   * This preserves whitespace, indentation, and line breaks
   * @param code The code to format
   * @returns Formatted code with preserved whitespace
   */
  formatCodeForDisplay(code: string): string {
    if (!code) return '';

    // Replace spaces with non-breaking spaces to preserve indentation
    // Replace < with &lt; and > with &gt; to prevent HTML interpretation
    return code
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/ /g, '&nbsp;')
      .replace(/\n/g, '<br>');
  }

  /**
   * Toggle the expansion state of a code log
   * @param log The log to toggle
   */
  toggleCodeExpansion(log: any): void {
    // Ensure the log has an ID
    if (!log.id) {
      log.id = `code-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    }

    // Check if this log is currently being typed
    const isCurrentlyTyping =
      this.isTypingLog &&
      this.currentLogIndex < this.formattedLogMessages.length &&
      this.formattedLogMessages[this.currentLogIndex].id === log.id;

    // If the log is currently being typed, don't allow collapsing
    if (isCurrentlyTyping && this.expandedCodeLogs.has(log.id)) {
      return;
    }

    // Check if the log has finished typing (visibleContent should match content)
    const hasFinishedTyping =
      log.visibleContent && log.content && log.visibleContent.length === log.content.length;

    // Toggle the expansion state
    if (this.expandedCodeLogs.has(log.id)) {
      // Only allow collapsing if typing is complete
      if (hasFinishedTyping || !isCurrentlyTyping) {
        this.expandedCodeLogs.delete(log.id);
      } else {

      }
    } else {
      // Always allow expanding
      this.expandedCodeLogs.add(log.id);
    }
  }

  /**
   * Check if a code log is expanded
   * @param id The ID of the log to check
   * @returns True if the log is expanded, false otherwise
   */
  isCodeExpanded(id: string): boolean {
    // Default to expanded (true) for all code logs
    return id ? this.expandedCodeLogs.has(id) : true;
  }

  /**
   * Get the appropriate layout key for a page index
   * This distributes layouts across pages when multiple layouts are available
   * @param index The page index
   * @returns The layout key to use for this page
   */
  getLayoutForPageIndex(index: number): string {
    if (!this.layoutData || this.layoutData.length === 0) {
      return '';
    }

    // If we only have one layout, use it for all pages
    if (this.layoutData.length === 1) {
      return this.layoutData[0];
    }

    // Distribute layouts evenly across pages
    return this.layoutData[index % this.layoutData.length];
  }

  /**
   * Extract error message from progress description
   * This is used to display error messages in the error page
   */
  extractErrorMessageFromProgressDescription(): void {
    // Get the latest status response from the polling service
    const statusResponse = this.pollingService.getLastStatusResponse();

    // First check if we have a direct response with progress_description in the details
    if (statusResponse &&
        statusResponse.details &&
        statusResponse.details.progress_description &&
        statusResponse.details.progress_description.trim() !== '') {

      this.errorDescription$.next(statusResponse.details.progress_description);
      this.errorTerminalOutput$.next(this.formatErrorOutput(statusResponse.details));
      return;
    }

    // Fallback to the stored progress description from the polling service
    const progressDescription = this.pollingService.getLastProgressDescription();
    if (progressDescription && progressDescription.trim() !== '') {

      // Try to extract error message from progress description
      try {
        // Check if it's a JSON string
        if (progressDescription.includes('{') && progressDescription.includes('}')) {
          const jsonStartIndex = progressDescription.indexOf('{');
          const jsonEndIndex = progressDescription.lastIndexOf('}') + 1;
          const jsonPart = progressDescription.substring(jsonStartIndex, jsonEndIndex);
          const parsedData = JSON.parse(jsonPart);

          // Extract error message from different possible fields using BehaviorSubjects
          if (parsedData.message) {
            this.errorDescription$.next(parsedData.message);
          } else if (parsedData.progress_description) {
            this.errorDescription$.next(parsedData.progress_description);
          } else if (parsedData.details && parsedData.details.message) {
            this.errorDescription$.next(parsedData.details.message);
          } else if (parsedData.details && parsedData.details.progress_description) {
            this.errorDescription$.next(parsedData.details.progress_description);
          } else if (parsedData.error) {
            this.errorDescription$.next(typeof parsedData.error === 'string'
              ? parsedData.error
              : JSON.stringify(parsedData.error));
          }

          // Format the terminal output for better readability using BehaviorSubject
          this.errorTerminalOutput$.next(this.formatErrorOutput(parsedData));
        } else {
          // Use the progress description as is using BehaviorSubjects
          this.errorDescription$.next(progressDescription);
          this.errorTerminalOutput$.next(progressDescription);
        }
      } catch (e) {
        this.logger.error('Error parsing progress description:', e);
        this.errorDescription$.next(progressDescription);
        this.errorTerminalOutput$.next(progressDescription);
      }
    }
  }

  /**
   * Format error output for better readability in the terminal
   * @param errorData The error data to format
   * @returns Formatted error output as a string
   */
  formatErrorOutput(errorData: any): string {
    try {
      // Create a formatted error object with highlighted sections
      const formattedError: any = {
        error_type: errorData.status || 'ERROR',
        timestamp: new Date().toISOString(),
        details: {}
      };

      // Extract error message
      if (errorData.message) {
        formattedError.message = errorData.message;
      } else if (errorData.progress_description) {
        formattedError.message = errorData.progress_description;
      }

      // Extract error details
      if (errorData.details) {
        formattedError.details = errorData.details;
      }

      // Extract log data if available
      if (errorData.log) {
        try {
          // Try to parse log as JSON
          if (typeof errorData.log === 'string' &&
              (errorData.log.includes('{') || errorData.log.includes('['))) {
            formattedError.log = JSON.parse(errorData.log);
          } else {
            formattedError.log = errorData.log;
          }
        } catch (e) {
          formattedError.log = errorData.log;
        }
      }

      // Add stack trace if available
      if (errorData.stack) {
        formattedError.stack_trace = errorData.stack;
      }

      // Format as pretty JSON with indentation
      return JSON.stringify(formattedError, null, 2);
    } catch (e) {
      this.logger.error('Error formatting error output:', e);
      // Fallback to simple JSON stringify
      return JSON.stringify(errorData, null, 2);
    }
  }

  // TrackBy functions for ngFor optimization
  trackByLogIndex(index: number, item: any): string {
    return item.id || index.toString();
  }

  trackByLayoutId(index: number, layout: any): string {
    return layout || index.toString();
  }

  trackByPageIndex(index: number): number {
    return index;
  }

  trackByArtifactId(index: number, artifact: any): string {
    return artifact.id || artifact.name || index.toString();
  }
}
