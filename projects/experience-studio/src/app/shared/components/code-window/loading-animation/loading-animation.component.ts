import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TextTransformationService } from '../../../services/text-transformation.service';

@Component({
  selector: 'app-loading-animation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './loading-animation.component.html',
  styleUrls: ['./loading-animation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingAnimationComponent implements OnInit, OnDestroy {
  @Input() messages: string[] = [];
  @Input() theme: 'light' | 'dark' = 'dark';

  // Reactive properties
  private destroy$ = new Subject<void>();
  currentMessage$ = new BehaviorSubject<string>('');

  private currentIndex = 0;
  private messageInterval: any;
  private transformedMessages: string[] = [];

  constructor(private textTransformationService: TextTransformationService) {}

  ngOnInit() {
    this.transformMessages();
    this.rotateMessages();
  }

  ngOnDestroy() {
    // Complete the destroy subject to clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();

    // Clear the interval when the component is destroyed
    if (this.messageInterval) {
      clearInterval(this.messageInterval);
    }
  }

  /**
   * Transform all messages to remove underscores and hyphens
   */
  private transformMessages() {
    // Transform the messages to remove underscores and hyphens
    this.transformedMessages = this.textTransformationService.transformMessages(this.messages);

    // If no messages are provided, use a default message
    if (this.transformedMessages.length === 0) {
      this.transformedMessages = ['Loading...'];
    }
  }

  private rotateMessages() {
    // Set initial message immediately using reactive pattern
    this.currentMessage$.next(this.transformedMessages[0]);

    // Only set up interval if there are multiple messages
    if (this.transformedMessages.length > 1) {
      this.messageInterval = setInterval(() => {
        this.currentIndex = (this.currentIndex + 1) % this.transformedMessages.length;
        this.currentMessage$.next(this.transformedMessages[this.currentIndex]);
      }, 3000);
    }
  }
}