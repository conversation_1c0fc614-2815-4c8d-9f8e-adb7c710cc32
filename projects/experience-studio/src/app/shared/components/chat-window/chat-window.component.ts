import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { CardsComponent, PromptBarComponent, IconsComponent } from '@awe/play-comp-library';
import { MarkdownModule } from 'ngx-markdown';
import { VerticalStepperComponent } from '../vertical-stepper/vertical-stepper.component';
import { StepperStateService } from '../../services/stepper-state.service';
import { Subscription } from 'rxjs';
import {createLogger} from '../../utils/logger';

@Component({
  selector: 'app-chat-window',
  standalone: true,
  imports: [CommonModule, PromptBarComponent, CardsComponent, MarkdownModule,VerticalStepperComponent,IconsComponent],
  templateUrl: './chat-window.component.html',
  styleUrls: ['./chat-window.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ChatWindowComponent implements AfterViewChecked, OnInit, OnDestroy {
  private isUserScrolling = false;
  private scrollTimeout: any = null;
  private mutationObserver: MutationObserver | null = null;
  private isNearBottom = true;
  private _showStepper: boolean = false;
  private subscriptions: Subscription[] = [];
  private logger = createLogger("ChatWindowComponent");

  // Stepper related properties
  showPreview: boolean = false;
  previewImage: { url: string, name: string } | null = null;
  private timeoutRefs: { [key: string]: any } = {};

  // Reference to the stepper component
  @ViewChild(VerticalStepperComponent) private stepperComponent?: VerticalStepperComponent;
  @Input()
  get showStepper(): boolean {
    return this._showStepper;
  }
  set showStepper(value: boolean) {
    if (this._showStepper !== value) {
      this._showStepper = value;
      // If stepper is shown and we have progress, update the stepper message
      if (value && this.progress) {
        this.updateStepperMessage();
      }
    }
  }

  private _progress: string = '';
  @Input()
  get progress(): string {
    return this._progress;
  }
  set progress(value: string) {
    if (this._progress !== value) {
      this._progress = value;
      // Update stepper message when progress changes
      if (this.showStepper && value) {
        this.updateStepperMessage();
        this.shouldScrollToBottom = true;
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      }
    }
  }

  private _progressDescription: string = '';
  @Input()
  get progressDescription(): string {
    return this._progressDescription;
  }
  set progressDescription(value: string) {
    if (this._progressDescription !== value) {
      this._progressDescription = value;
      // Update stepper message when progress description changes
      if (this.showStepper && value) {
        this.updateStepperMessage();
        this.shouldScrollToBottom = true;
        setTimeout(() => {
          this.scrollToBottom();
        }, 100);
      }
    }
  }

  @Input() status: string = 'PENDING';

  private _selectedImageDataUri: string = '';
  @Input()
  get selectedImageDataUri(): string {
    return this._selectedImageDataUri;
  }
  set selectedImageDataUri(value: string) {
    if (this._selectedImageDataUri !== value) {
      this._selectedImageDataUri = value;
      // If we have a new image, update the chat messages to include it
      if (value) {
        this.addImageToLastUserMessage();
      }
      this.cdr.detectChanges();
    }
  }

  @Input() defaultText: string = '';
  @Input() leftIcons: { name: string; status: 'active' | 'default' | 'disable' }[] = [];
  @Input() rightIcons: { name: string; status: 'active' | 'default' | 'disable' }[] = [];
  @Input() isCodeGenerationComplete: boolean = false;

  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    private stepperStateService: StepperStateService
  ) {}
  private _theme: 'light' | 'dark' = 'light';
  @Input()
  get theme(): 'light' | 'dark' {
    return this._theme;
  }
  set theme(value: 'light' | 'dark') {
    this._theme = value;
    this.updateMessagesTheme();
    // Set flag to update card themes in next view check cycle
    this.shouldUpdateCardThemes = true;
    // Force update the theme on all cards when theme changes
    setTimeout(() => {
      this.forceUpdateCardThemes();
    }, 0);
  }

  private _chatMessages: { text: string, from: 'user' | 'ai', theme: 'light' | 'dark', hasSteps?: boolean, imageDataUri?: string }[] = [];
  private shouldScrollToBottom = true;

  /**
   * Adds the selected image to the last user message in the chat
   * If no user message exists, it creates a new one
   */
  private addImageToLastUserMessage(): void {
    if (!this._selectedImageDataUri) return;

    // Find the last user message
    const lastUserMessageIndex = [...this._chatMessages].reverse().findIndex(msg => msg.from === 'user');

    if (lastUserMessageIndex >= 0) {
      // Convert reverse index to actual index
      const actualIndex = this._chatMessages.length - 1 - lastUserMessageIndex;
      // Update the last user message to include the image
      this._chatMessages[actualIndex].imageDataUri = this._selectedImageDataUri;
    } else {
      // If no user message exists, create a new one with the image
      this._chatMessages.push({
        text: '',
        from: 'user',
        theme: this.theme,
        imageDataUri: this._selectedImageDataUri
      });
    }

    // Force scroll to bottom after adding the image
    this.shouldScrollToBottom = true;
    setTimeout(() => {
      this.scrollToBottom();
    }, 100);
  }

  /**
   * Updates the theme of all messages to match the component's theme
   * Note: We're now using the component's theme directly in the template,
   * but we'll keep this method for backward compatibility
   */
  private updateMessagesTheme(): void {
    if (this._chatMessages && this._chatMessages.length > 0) {
      this._chatMessages.forEach(message => {
        message.theme = this._theme;
      });
    }

    // Force change detection to update the view
    setTimeout(() => {
      // This timeout ensures the theme changes are applied after the current execution cycle
    }, 0);
  }

  @Input()
  get chatMessages(): { text: string, from: 'user' | 'ai', theme: 'light' | 'dark', hasSteps?: boolean, imageDataUri?: string }[] {
    return this._chatMessages;
  }
  set chatMessages(value: { text: string, from: 'user' | 'ai', theme: 'light' | 'dark', hasSteps?: boolean, imageDataUri?: string }[]) {
    // Check if messages have been added or changed
    const messagesAdded = value && this._chatMessages && value.length !== this._chatMessages.length;

    // Check if the last message content has changed (for streaming responses)
    let lastMessageChanged = false;
    if (value && this._chatMessages && value.length > 0 && this._chatMessages.length > 0) {
      const newLastMessage = value[value.length - 1];
      const oldLastMessage = this._chatMessages[this._chatMessages.length - 1];
      if (
        newLastMessage &&
        oldLastMessage &&
        newLastMessage.from === oldLastMessage.from &&
        newLastMessage.text !== oldLastMessage.text
      ) {
        lastMessageChanged = true;
      }
    }

    // Always scroll to bottom when new messages are added (similar to React implementation)
    if (messagesAdded) {
      this.shouldScrollToBottom = true;
      this.isUserScrolling = false; // Reset user scrolling when new messages arrive
    }
    // For content changes, only scroll if user is near bottom or hasn't manually scrolled
    else if (lastMessageChanged) {
      if (!this.isUserScrolling || this.isScrolledNearBottom()) {
        this.shouldScrollToBottom = true;
      }
    }

    // Ensure each message has the correct theme
    if (value) {
      value.forEach(message => {
        if (!message.theme) {
          message.theme = this.theme;
        }
      });
    }

    this._chatMessages = value;

    // If new messages were added or content changed, force update the theme on all cards
    if (messagesAdded || lastMessageChanged) {
      // Use a shorter timeout for better responsiveness
      setTimeout(() => {
        this.forceUpdateCardThemes();

        // Ensure we scroll to bottom after rendering new messages
        if (this.shouldScrollToBottom) {
          this.scrollToBottom();

          // Add a second scroll after a short delay to handle any late DOM updates
          // This mimics the React useEffect behavior that runs after DOM updates
          setTimeout(() => {
            this.scrollToBottom();
          }, 50);
        }
      }, 10); // Shorter delay for better responsiveness
    }
  }

  private _textValue: string = '';
  @Input()
  get textValue(): string {
    return this._textValue;
  }
  set textValue(value: string) {
    this._textValue = value;
    this.textValueChange.emit(this._textValue);
  }

  @Output() textValueChange = new EventEmitter<string>();
  @Output() enterPressed = new EventEmitter<void>();
  @Output() iconClicked = new EventEmitter<{
    name: string;
    side: string;
    index: number;
    theme: string;
  }>();
  @Output() imageTransferred = new EventEmitter<void>();
  @Output() retryStep = new EventEmitter<number>();

  @ViewChild('chatScrollContainer') private chatScrollContainer!: ElementRef;

  handleIconClick(event: { name: string; side: string; index: number }): void {
    // If code generation is not complete, don't allow icon clicks
    if (!this.isCodeGenerationComplete) {
      return;
    }

    this.iconClicked.emit({ ...event, theme: this.theme });
  }

  /**
   * Shows the image preview overlay
   * @param imageUrl URL of the image to preview
   * @param imageName Name of the image (optional)
   */
  showImagePreview(imageUrl: string, imageName: string = 'Image'): void {
    this.previewImage = { url: imageUrl, name: imageName };
    this.showPreview = true;
    this.cdr.detectChanges();
  }

  /**
   * Closes the image preview overlay
   */
  closeImagePreview(): void {
    this.showPreview = false;
    this.previewImage = null;
    this.cdr.detectChanges();
  }

  /**
   * Gets the appropriate icon color based on the current theme
   */
  getIconColor(): string {
    return this.theme === 'dark' ? 'var(--icon-enabled-color)' : 'var(--icon-enabled-color)';
  }

  /**
   * Handle step updates from the vertical stepper component
   * @param stepIndex The index of the updated step
   */
  onStepUpdated(stepIndex: number): void {
    // You can add additional logic here if needed
  }

  /**
   * Handle retry button click from the vertical stepper component
   * @param stepIndex The index of the step to retry
   */
  onRetryStep(stepIndex: number): void {
   
    // Change the status to IN_PROGRESS to show loading animation
    this.status = 'IN_PROGRESS';

    // Emit the retry event to the parent component
    this.retryStep.emit(stepIndex);

    // Force change detection to update the UI
    this.cdr.detectChanges();
  }

  /**
   * Public method to manually trigger scrolling to bottom
   * Can be called from parent components when needed
   */
  scrollToBottomManually(): void {
    this.shouldScrollToBottom = true;
    this.isUserScrolling = false; // Reset user scrolling flag

    // We need to trigger change detection to run ngAfterViewChecked
    // Use a slightly longer timeout to ensure DOM is ready
    setTimeout(() => {
      this.scrollToBottom();
      // Force another scroll after a short delay to handle any late DOM updates
      setTimeout(() => {
        this.scrollToBottom();
      }, 100);
    }, 0);
  }

  ngOnInit(): void {
    // We need to wait for the view to be initialized before adding the scroll listener
    setTimeout(() => {
      this.setupScrollListener();
      // Ensure messages have the correct theme
      this.updateMessagesTheme();
      // Force update the theme on all cards
      this.forceUpdateCardThemes();
      // Set up mutation observer to detect content changes
      this.setupMutationObserver();

      // Initial scroll to bottom (similar to React's useEffect with empty dependency array)
      this.shouldScrollToBottom = true;
      this.scrollToBottom();

      // Add a second scroll after a short delay to handle any late DOM updates
      setTimeout(() => {
        this.scrollToBottom();
      }, 100);
    }, 0);

    // Subscribe to stepper reset events
    this.subscriptions.push(
      this.stepperStateService.resetStepper$.subscribe(shouldReset => {
        if (shouldReset) {
          this.resetStepperState();
        }
      })
    );
  }

  /**
   * Resets the stepper state
   * This is called when the stepper state service emits a reset event
   */
  private resetStepperState(): void {
    // Reset the stepper component if it exists
    if (this.stepperComponent) {
      this.stepperComponent.resetStepper();
    }

    // Reset the stepper message in the chat
    const stepperMessageIndex = this._chatMessages.findIndex(msg => msg.hasSteps);
    if (stepperMessageIndex >= 0) {
      // Remove the stepper message from the chat
      this._chatMessages.splice(stepperMessageIndex, 1);
      this.cdr.detectChanges();
    }

    // Reset the stepper inputs
    this._progress = '';
    this._progressDescription = '';
    this.status = 'PENDING';

    // Force change detection
    this.cdr.detectChanges();
  }

  /**
   * Sets up a MutationObserver to detect changes in the chat content
   * This helps us auto-scroll when message content changes, not just when new messages are added
   */
  private setupMutationObserver(): void {
    const container = this.chatScrollContainer?.nativeElement;
    if (container && window.MutationObserver) {
      // Use ngZone.runOutsideAngular to avoid triggering change detection on mutations
      this.ngZone.runOutsideAngular(() => {
        this.mutationObserver = new MutationObserver(mutations => {
          // Check if content was added (similar to React's messagesEndRef behavior)
          const contentAdded = mutations.some(
            mutation => mutation.type === 'childList' && mutation.addedNodes.length > 0
          );

          // If content was added or we're near the bottom and not manually scrolling
          if (contentAdded || (this.isScrolledNearBottom() && !this.isUserScrolling)) {
            this.shouldScrollToBottom = true;

            // Run inside Angular zone to trigger change detection
            this.ngZone.run(() => {
              // Use double requestAnimationFrame for smoother scrolling
              requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                  this.scrollToBottom();
                  this.cdr.detectChanges();
                });
              });
            });
          }
        });

        // Observe changes to the container's children (added/removed nodes and subtree changes)
        this.mutationObserver.observe(container, {
          childList: true,
          subtree: true,
          characterData: true,
          attributes: false,
        });
      });
    }
  }

  /**
   * Forces an update of the theme on all cards by directly manipulating the DOM
   * This is a fallback method to ensure the theme is applied correctly
   */
  private forceUpdateCardThemes(): void {
    setTimeout(() => {
      try {
        // Get all card elements within this component
        const cardElements = document.querySelectorAll('.chat-wrapper awe-cards .awe-card');

        // Apply the appropriate theme class to each card
        cardElements.forEach(card => {
          // Remove existing theme classes
          card.classList.remove('awe-card--light', 'awe-card--dark');

          // Add the current theme class
          card.classList.add(`awe-card--${this._theme}`);

          // Force the background color based on the card type and theme
          const isUserCard = card.closest('.user-card') !== null;
          const isAiCard = card.closest('.ai-card') !== null;

          if (this._theme === 'dark') {
            if (isUserCard) {
              (card as HTMLElement).style.backgroundColor = 'var(--color-background-dark)';
              (card as HTMLElement).style.color = 'var(--text-white)';
            } else if (isAiCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--color-background-secondary-light)';
              (card as HTMLElement).style.color = 'var(--text-white)';
            }
          } else {
            if (isUserCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--Neutral-N-100, var(--color-background-light))';
              (card as HTMLElement).style.color = 'var(--text-black)';
            } else if (isAiCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--Neutral-N-50, var(--color-background-light))';
              (card as HTMLElement).style.color = 'var(--text-black)';
            }
          }
        });
      } catch (error) {
        this.logger.error('Error updating card themes:', error);
      }
    }, 100); // Small delay to ensure DOM is ready
  }

  /**
   * Checks if the scroll container is near the bottom
   * @returns true if the container is scrolled to near the bottom
   */
  private isScrolledNearBottom(): boolean {
    const container = this.chatScrollContainer?.nativeElement;
    if (!container) return true;

    // Consider "near bottom" if within 50px of the bottom
    const threshold = 50;
    const distanceFromBottom =
      container.scrollHeight - container.scrollTop - container.clientHeight;
    return distanceFromBottom <= threshold;
  }

  /**
   * Sets up the scroll event listener to detect when user is manually scrolling
   */
  private setupScrollListener(): void {
    const container = this.chatScrollContainer?.nativeElement;
    if (container) {
      // Use ngZone.runOutsideAngular to avoid triggering change detection on scroll
      this.ngZone.runOutsideAngular(() => {
        container.addEventListener('scroll', () => {
          // Update the isNearBottom flag
          this.isNearBottom = this.isScrolledNearBottom();

          // If user is not near bottom, mark as user scrolling
          if (!this.isNearBottom) {
            this.isUserScrolling = true;

            // Reset the user scrolling flag after a delay
            if (this.scrollTimeout) {
              clearTimeout(this.scrollTimeout);
            }

            this.scrollTimeout = setTimeout(() => {
              // If user has scrolled back to bottom, reset the flag immediately
              if (this.isScrolledNearBottom()) {
                this.isUserScrolling = false;
                this.shouldScrollToBottom = true;

                // Run inside Angular zone to trigger change detection
                this.ngZone.run(() => {
                  this.scrollToBottom();
                  this.cdr.detectChanges();
                });
              } else {
                // Otherwise, keep the flag for a bit longer
                this.scrollTimeout = setTimeout(() => {
                  this.isUserScrolling = false;
                }, 1000);
              }
            }, 300);
          } else {
            // User is at the bottom, so they're not manually scrolling away
            this.isUserScrolling = false;
          }
        });
      });
    }
  }

  ngOnDestroy(): void {
    // Clear any timeouts to prevent memory leaks
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    // Disconnect and clean up the mutation observer
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }

    // Clear stepper timeouts
    this.clearAllTimeouts();

    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];

    // We can't easily remove the exact same scroll event listener we added,
    // but we've cleaned up the timeouts and observers which is the main concern for memory leaks
  }

  // We'll set up the scroll event listener in ngOnInit instead of using HostListener

  ngAfterViewChecked(): void {
    // Only auto-scroll if we should scroll to bottom and user is not manually scrolling
    if (this.shouldScrollToBottom && !this.isUserScrolling) {
      this.scrollToBottom();
    }

    // Check if we need to update card themes (only do this occasionally to avoid performance issues)
    if (this.shouldUpdateCardThemes) {
      this.shouldUpdateCardThemes = false;
      this.forceUpdateCardThemes();
    }
  }

  /**
   * Manually trigger a scroll to bottom - can be called from parent components
   * This is useful when new messages are added programmatically
   */
  public scrollToBottomOfChat(): void {
    this.shouldScrollToBottom = true;
    this.isUserScrolling = false;

    // Use setTimeout to ensure this runs after the current execution cycle
    setTimeout(() => {
      this.scrollToBottom();
    }, 0);
  }

  // Flag to track if we need to update card themes
  private shouldUpdateCardThemes = false;

  /**
   * Scrolls the chat container to the bottom
   * Uses requestAnimationFrame for smoother scrolling and to ensure DOM is ready
   */
  scrollToBottom(): void {
    try {
      const container = this.chatScrollContainer?.nativeElement;
      if (container) {
        // Use requestAnimationFrame to ensure DOM is fully rendered before scrolling
        requestAnimationFrame(() => {
          // Double RAF for extra safety on complex DOM updates
          requestAnimationFrame(() => {
            // Use smooth scrolling behavior
            // Note: We've added scroll-behavior: smooth to the CSS
            // but we'll also use scrollIntoView for better browser support
            const lastElement = container.lastElementChild;
            if (lastElement) {
              lastElement.scrollIntoView({
                behavior: 'smooth',
                block: 'end'
              });
            } else {
              // Fallback to traditional scrolling if no last element
              container.scrollTop = container.scrollHeight;
            }

            // Reset the flag after scrolling
            this.shouldScrollToBottom = false;
            // Force change detection to ensure UI updates
            this.cdr.detectChanges();
          });
        });
      }
    } catch (err) {
      this.logger.error('Auto-scroll failed:', err);
    }
  }

  /**
   * Adds or updates a stepper message in the chat
   * If a stepper message already exists, it will be updated
   * Otherwise, a new message will be added
   */
  updateStepperMessage(): void {
    // Check if we already have a message with steps
    const stepperMessageIndex = this._chatMessages.findIndex(msg => msg.hasSteps);

    if (stepperMessageIndex >= 0) {
      // Update existing message
      // We don't need to update the text as the stepper component will handle its own state
      this._chatMessages[stepperMessageIndex].hasSteps = true;
    } else if (this.showStepper && this.progress) {
      // Add a new message with steps (no intro text)
      this._chatMessages.push({
        text: '', // Empty text to remove the intro
        from: 'ai',
        theme: this.theme,
        hasSteps: true,
      });

      // Trigger scroll to bottom
      this.shouldScrollToBottom = true;
      setTimeout(() => {
        this.scrollToBottom();
      }, 100);
    }

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  // No longer need stepper-related methods as we're using the VerticalStepperComponent

  /**
   * Clear all timeouts
   */
  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }

  // No longer need these methods as we're using the VerticalStepperComponent
}
