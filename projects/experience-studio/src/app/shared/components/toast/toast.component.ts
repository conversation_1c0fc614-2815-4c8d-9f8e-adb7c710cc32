import { Component, Input, OnInit, Output, EventEmitter, HostBinding, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { ThemeService } from '../../services/theme-service/theme.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

@Component({
  selector: 'exp-toast',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './toast.component.html',
  styleUrls: ['./toast.component.scss'],
  animations: [
    trigger('toastAnimation', [
      state('void', style({
        transform: 'translateY(20px)',
        opacity: 0
      })),
      state('visible', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      state('hidden', style({
        transform: 'translateY(20px)',
        opacity: 0
      })),
      transition('void => visible', animate('300ms ease-out')),
      transition('visible => hidden', animate('300ms ease-in'))
    ])
  ]
})
export class ToastComponent implements OnInit, OnDestroy {
  @Input() message: string = '';
  @Input() type: ToastType = 'info';
  @Input() duration: number = 3000;
  @Output() closed = new EventEmitter<void>();

  @HostBinding('@toastAnimation') animationState: 'visible' | 'hidden' = 'visible';

  theme: 'light' | 'dark' = 'light';
  private autoCloseTimeout: any;

  private subscriptionManager = new SubscriptionManager();

  constructor(private themeService: ThemeService) {}

  ngOnInit(): void {
    // Subscribe to theme changes
    this.theme = this.themeService.getCurrentTheme();
    this.subscriptionManager.subscribe(
      this.themeService.themeObservable,
      theme => {
        this.theme = theme;
      }
    );

    // Auto-close after duration
    if (this.duration > 0) {
      this.autoCloseTimeout = setTimeout(() => {
        this.close();
      }, this.duration);
    }
  }

  ngOnDestroy(): void {
    if (this.autoCloseTimeout) {
      clearTimeout(this.autoCloseTimeout);
    }
  }

  close(): void {
    this.animationState = 'hidden';
    setTimeout(() => {
      this.closed.emit();
    }, 300); // Wait for animation to complete
  }

  getTitle(): string {
    switch (this.type) {
      case 'success':
        return 'Generated Successfully';
      case 'error':
        return 'Error Occurred';
      case 'warning':
        return 'Action Required';
      case 'info':
      default:
        return 'Information';
    }
  }

  getIcon(): string {
    const basePath = '/assets/icons/';

    switch (this.type) {
      case 'success':
        return `<img src="${basePath}awe_correct_t.svg" alt="Success" width="24" height="24">`;
      case 'error':
        return `<img src="${basePath}awe_close_t.svg" alt="Error" width="24" height="24">`;
      case 'warning':
        return `<img src="${basePath}awe_w_info_t.svg" alt="Info" width="24" height="24">`;
      case 'info':
        return `<img src="${basePath}awe_info_t.svg" alt="Info" width="24" height="24">`;
      default:
        return `<img src="${basePath}awe_info_t.svg" alt="Info" width="24" height="24">`;
    }
  }
}
