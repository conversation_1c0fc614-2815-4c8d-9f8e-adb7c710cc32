import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { 
  TypewriterService, 
  TypewriterMode, 
  TypewriterState, 
  ContentType 
} from '../../services/typewriter.service';
import { SubscriptionManager } from '../../utils/subscription-manager';

/**
 * Demo component showcasing all typewriter functionality
 */
@Component({
  selector: 'app-typewriter-demo',
  templateUrl: './typewriter-demo.component.html',
  styleUrls: ['./typewriter-demo.component.scss']
})
export class TypewriterDemoComponent implements OnInit, OnDestroy {
  // Expose enums to template
  TypewriterMode = TypewriterMode;
  ContentType = ContentType;
  TypewriterState = TypewriterState;

  // Demo texts
  singleText = 'Welcome to the Enhanced Typewriter Service!';
  cyclingTexts = [
    'Create amazing applications',
    'Build beautiful interfaces', 
    'Design stunning experiences',
    'Develop with confidence'
  ];
  sequenceTexts = [
    'Step 1: Initialize the project',
    'Step 2: Configure dependencies', 
    'Step 3: Build the application',
    'Step 4: Deploy to production'
  ];
  codeTexts = [
    'const message = "Hello World!";',
    'function greet(name) { return `Hello, ${name}!`; }',
    'console.log(greet("Developer"));'
  ];

  // Demo states
  currentStates: { [key: string]: TypewriterState } = {};
  currentTexts: { [key: string]: string } = {};
  
  // Instance IDs for different demos
  instanceIds: { [key: string]: string } = {};

  // Batch demo
  batchInstanceId?: string;
  batchLogs: string[] = [];

  private subscriptionManager = new SubscriptionManager();

  constructor(private typewriterService: TypewriterService) {}

  ngOnInit(): void {
    this.initializeDemos();
  }

  ngOnDestroy(): void {
    // Clean up all instances
    Object.values(this.instanceIds).forEach(id => {
      if (id) this.typewriterService.stop(id);
    });
    if (this.batchInstanceId) {
      this.typewriterService.stop(this.batchInstanceId);
    }
    this.subscriptionManager.unsubscribeAll();
  }

  private initializeDemos(): void {
    // Single text demo
    this.createDemo('single', [this.singleText], {
      mode: TypewriterMode.SINGLE,
      typingSpeed: 50,
      showCursor: true
    });

    // Cycling demo
    this.createDemo('cycling', this.cyclingTexts, {
      mode: TypewriterMode.CYCLING,
      typingSpeed: 60,
      erasingSpeed: 40,
      pauseBeforeErasing: 2000,
      pauseBeforeTyping: 500,
      shuffle: true
    });

    // Sequence demo
    this.createDemo('sequence', this.sequenceTexts, {
      mode: TypewriterMode.SEQUENCE,
      typingSpeed: 40,
      pauseBeforeTyping: 800
    });

    // Code demo
    this.createDemo('code', this.codeTexts, {
      mode: TypewriterMode.SEQUENCE,
      contentType: ContentType.CODE,
      typingSpeed: 30,
      pauseBeforeTyping: 1000
    });

    // Batch demo (for logs)
    this.batchInstanceId = this.typewriterService.createBatch({
      contentType: ContentType.TEXT,
      typingSpeed: 20,
      pauseBeforeTyping: 200
    });

    if (this.batchInstanceId) {
      this.subscribeToInstance('batch', this.batchInstanceId);
    }
  }

  private createDemo(key: string, texts: string[], config: any): void {
    const instanceId = this.typewriterService.createInstance(texts, {
      ...config,
      onStateChange: (state: TypewriterState) => {
        this.currentStates[key] = state;
      }
    });

    this.instanceIds[key] = instanceId;
    this.subscribeToInstance(key, instanceId);
    this.typewriterService.start(instanceId);
  }

  private subscribeToInstance(key: string, instanceId: string): void {
    const textObservable = this.typewriterService.getText$(instanceId);
    const stateObservable = this.typewriterService.getState$(instanceId);

    if (textObservable) {
      this.subscriptionManager.subscribe(
        textObservable,
        (text: string) => {
          this.currentTexts[key] = text;
        }
      );
    }

    if (stateObservable) {
      this.subscriptionManager.subscribe(
        stateObservable,
        (state: TypewriterState) => {
          this.currentStates[key] = state;
        }
      );
    }
  }

  // Control methods
  pauseDemo(key: string): void {
    const instanceId = this.instanceIds[key];
    if (instanceId) {
      this.typewriterService.pause(instanceId);
    }
  }

  resumeDemo(key: string): void {
    const instanceId = this.instanceIds[key];
    if (instanceId) {
      this.typewriterService.resume(instanceId);
    }
  }

  restartDemo(key: string): void {
    const instanceId = this.instanceIds[key];
    if (instanceId) {
      this.typewriterService.stop(instanceId);
      
      // Recreate the demo
      setTimeout(() => {
        switch (key) {
          case 'single':
            this.createDemo(key, [this.singleText], {
              mode: TypewriterMode.SINGLE,
              typingSpeed: 50,
              showCursor: true
            });
            break;
          case 'cycling':
            this.createDemo(key, this.cyclingTexts, {
              mode: TypewriterMode.CYCLING,
              typingSpeed: 60,
              erasingSpeed: 40,
              pauseBeforeErasing: 2000,
              pauseBeforeTyping: 500,
              shuffle: true
            });
            break;
          case 'sequence':
            this.createDemo(key, this.sequenceTexts, {
              mode: TypewriterMode.SEQUENCE,
              typingSpeed: 40,
              pauseBeforeTyping: 800
            });
            break;
          case 'code':
            this.createDemo(key, this.codeTexts, {
              mode: TypewriterMode.SEQUENCE,
              contentType: ContentType.CODE,
              typingSpeed: 30,
              pauseBeforeTyping: 1000
            });
            break;
        }
      }, 100);
    }
  }

  // Batch demo methods
  addLogEntry(): void {
    if (this.batchInstanceId) {
      const logMessages = [
        'Application started successfully',
        'Loading configuration files...',
        'Connecting to database...',
        'Initializing user interface...',
        'Ready to accept requests',
        'Processing user input...',
        'Generating response...',
        'Operation completed successfully'
      ];
      
      const randomMessage = logMessages[Math.floor(Math.random() * logMessages.length)];
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = `[${timestamp}] ${randomMessage}`;
      
      this.batchLogs.push(logEntry);
      this.typewriterService.addText(this.batchInstanceId, logEntry);
    }
  }

  clearBatchLogs(): void {
    if (this.batchInstanceId) {
      this.typewriterService.stop(this.batchInstanceId);
      this.batchLogs = [];
      this.currentTexts['batch'] = '';
      
      // Recreate batch instance
      this.batchInstanceId = this.typewriterService.createBatch({
        contentType: ContentType.TEXT,
        typingSpeed: 20,
        pauseBeforeTyping: 200
      });
      
      if (this.batchInstanceId) {
        this.subscribeToInstance('batch', this.batchInstanceId);
      }
    }
  }

  getStateColor(state: TypewriterState): string {
    switch (state) {
      case TypewriterState.TYPING: return '#28a745';
      case TypewriterState.PAUSED: return '#ffc107';
      case TypewriterState.ERASING: return '#fd7e14';
      case TypewriterState.COMPLETED: return '#6c757d';
      case TypewriterState.ERROR: return '#dc3545';
      default: return '#6c757d';
    }
  }
}
