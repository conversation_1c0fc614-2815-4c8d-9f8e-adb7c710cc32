<div class="typewriter-demo">
  <h1>Enhanced Typewriter Service Demo</h1>
  <p>This demo showcases all the features of the enhanced typewriter service.</p>

  <!-- Single Text Demo -->
  <div class="demo-section">
    <h2>Single Text Animation</h2>
    <p>Types text once and stops.</p>
    
    <div class="demo-container">
      <div class="output-area">
        <div class="typewriter-output">{{ currentTexts['single'] || '' }}</div>
      </div>
      
      <div class="controls">
        <div class="status">
          Status: 
          <span [style.color]="getStateColor(currentStates['single'])">
            {{ currentStates['single'] || 'idle' }}
          </span>
        </div>
        <button (click)="pauseDemo('single')" [disabled]="currentStates['single'] !== TypewriterState.TYPING">
          Pause
        </button>
        <button (click)="resumeDemo('single')" [disabled]="currentStates['single'] !== TypewriterState.PAUSED">
          Resume
        </button>
        <button (click)="restartDemo('single')">Restart</button>
      </div>
    </div>
  </div>

  <!-- Cycling Text Demo -->
  <div class="demo-section">
    <h2>Cycling Text Animation</h2>
    <p>Types text, erases it, then moves to the next text in a loop.</p>
    
    <div class="demo-container">
      <div class="output-area">
        <div class="typewriter-output">{{ currentTexts['cycling'] || '' }}</div>
      </div>
      
      <div class="controls">
        <div class="status">
          Status: 
          <span [style.color]="getStateColor(currentStates['cycling'])">
            {{ currentStates['cycling'] || 'idle' }}
          </span>
        </div>
        <button (click)="pauseDemo('cycling')" [disabled]="currentStates['cycling'] !== TypewriterState.TYPING && currentStates['cycling'] !== TypewriterState.ERASING">
          Pause
        </button>
        <button (click)="resumeDemo('cycling')" [disabled]="currentStates['cycling'] !== TypewriterState.PAUSED">
          Resume
        </button>
        <button (click)="restartDemo('cycling')">Restart</button>
      </div>
    </div>
  </div>

  <!-- Sequence Text Demo -->
  <div class="demo-section">
    <h2>Sequence Text Animation</h2>
    <p>Types multiple texts in sequence without erasing.</p>
    
    <div class="demo-container">
      <div class="output-area">
        <div class="typewriter-output sequence">{{ currentTexts['sequence'] || '' }}</div>
      </div>
      
      <div class="controls">
        <div class="status">
          Status: 
          <span [style.color]="getStateColor(currentStates['sequence'])">
            {{ currentStates['sequence'] || 'idle' }}
          </span>
        </div>
        <button (click)="pauseDemo('sequence')" [disabled]="currentStates['sequence'] !== TypewriterState.TYPING">
          Pause
        </button>
        <button (click)="resumeDemo('sequence')" [disabled]="currentStates['sequence'] !== TypewriterState.PAUSED">
          Resume
        </button>
        <button (click)="restartDemo('sequence')">Restart</button>
      </div>
    </div>
  </div>

  <!-- Code Demo -->
  <div class="demo-section">
    <h2>Code Animation</h2>
    <p>Optimized for typing code with adaptive speeds for different characters.</p>
    
    <div class="demo-container">
      <div class="output-area">
        <pre class="typewriter-output code">{{ currentTexts['code'] || '' }}</pre>
      </div>
      
      <div class="controls">
        <div class="status">
          Status: 
          <span [style.color]="getStateColor(currentStates['code'])">
            {{ currentStates['code'] || 'idle' }}
          </span>
        </div>
        <button (click)="pauseDemo('code')" [disabled]="currentStates['code'] !== TypewriterState.TYPING">
          Pause
        </button>
        <button (click)="resumeDemo('code')" [disabled]="currentStates['code'] !== TypewriterState.PAUSED">
          Resume
        </button>
        <button (click)="restartDemo('code')">Restart</button>
      </div>
    </div>
  </div>

  <!-- Batch/Logs Demo -->
  <div class="demo-section">
    <h2>Batch Animation (Logs)</h2>
    <p>Add text dynamically and animate in sequence (perfect for logs).</p>
    
    <div class="demo-container">
      <div class="output-area">
        <div class="typewriter-output logs">{{ currentTexts['batch'] || 'No logs yet...' }}</div>
      </div>
      
      <div class="controls">
        <div class="status">
          Status: 
          <span [style.color]="getStateColor(currentStates['batch'])">
            {{ currentStates['batch'] || 'idle' }}
          </span>
        </div>
        <button (click)="addLogEntry()">Add Log Entry</button>
        <button (click)="clearBatchLogs()">Clear Logs</button>
      </div>
    </div>
  </div>

  <!-- Component Demo -->
  <div class="demo-section">
    <h2>Typewriter Component</h2>
    <p>Using the standalone typewriter component.</p>
    
    <div class="demo-container">
      <div class="output-area">
        <app-typewriter 
          [texts]="['Component Demo', 'Easy to use!', 'Highly customizable']"
          [mode]="TypewriterMode.CYCLING"
          [showCursor]="true"
          [cursorChar]="'_'"
          [typingSpeed]="40"
          theme="light">
        </app-typewriter>
      </div>
    </div>
  </div>

  <!-- Directive Demo -->
  <div class="demo-section">
    <h2>Typewriter Directive</h2>
    <p>Using the typewriter directive on any element.</p>
    
    <div class="demo-container">
      <div class="output-area">
        <h3 
          appTypewriter
          [texts]="['Directive Magic!', 'Works on any element', 'Super flexible']"
          [mode]="TypewriterMode.CYCLING"
          [typingSpeed]="50"
          [showCursor]="true">
        </h3>
        
        <input 
          type="text" 
          appTypewriter
          [texts]="['Type your message...', 'Enter some text...', 'What\'s on your mind?']"
          [mode]="TypewriterMode.CYCLING"
          [targetAttribute]="'placeholder'"
          [typingSpeed]="60"
          placeholder="Loading...">
      </div>
    </div>
  </div>

  <!-- Performance Info -->
  <div class="demo-section">
    <h2>Performance & Features</h2>
    <ul>
      <li>✅ Multiple concurrent animations</li>
      <li>✅ Adaptive typing speeds for different content types</li>
      <li>✅ Memory efficient with proper cleanup</li>
      <li>✅ Observable-based reactive updates</li>
      <li>✅ Backward compatible with existing code</li>
      <li>✅ Accessible with reduced motion support</li>
      <li>✅ Customizable cursors and themes</li>
      <li>✅ Pause/resume functionality</li>
    </ul>
  </div>
</div>
