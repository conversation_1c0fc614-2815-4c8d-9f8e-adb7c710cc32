.typewriter-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 1rem;
    font-size: 2.5rem;
    font-weight: 300;
  }

  > p {
    text-align: center;
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 3rem;
  }
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  h2 {
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    font-weight: 500;
  }

  > p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
  }
}

.demo-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: flex-start;
  }
}

.output-area {
  flex: 1;
  min-height: 120px;
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
    opacity: 0.3;
  }
}

.typewriter-output {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #212529;
  min-height: 1.5em;
  word-wrap: break-word;

  &.sequence {
    white-space: pre-line;
  }

  &.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 0.9rem;
    white-space: pre;
    overflow-x: auto;
  }

  &.logs {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    white-space: pre-line;
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 1rem;
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
  }
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 200px;

  @media (min-width: 768px) {
    flex-shrink: 0;
  }
}

.status {
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
  padding: 0.5rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  text-align: center;

  span {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
  }
}

button {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #007bff;
  color: white;

  &:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  }

  &:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: none;
  }

  &:nth-child(2) {
    background: #ffc107;
    color: #212529;

    &:hover:not(:disabled) {
      background: #e0a800;
    }
  }

  &:nth-child(3) {
    background: #28a745;

    &:hover:not(:disabled) {
      background: #1e7e34;
    }
  }

  &:nth-child(4) {
    background: #dc3545;

    &:hover:not(:disabled) {
      background: #c82333;
    }
  }
}

// Input styling for directive demo
input[appTypewriter] {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
}

// Performance info styling
.demo-section:last-child {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  h2 {
    color: white;
  }

  ul {
    list-style: none;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;

    li {
      padding: 0.75rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      font-size: 0.9rem;
      font-weight: 500;
    }
  }
}

// Responsive design
@media (max-width: 767px) {
  .typewriter-demo {
    padding: 1rem;

    h1 {
      font-size: 2rem;
    }
  }

  .demo-section {
    padding: 1.5rem;
  }

  .output-area {
    min-height: 80px;
    padding: 1rem;
  }

  .typewriter-output {
    font-size: 1rem;

    &.code, &.logs {
      font-size: 0.8rem;
    }
  }

  .controls {
    min-width: auto;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .typewriter-demo {
    background: #1a1a1a;
    color: #e9ecef;

    h1 {
      color: #f8f9fa;
    }

    > p {
      color: #adb5bd;
    }
  }

  .demo-section {
    background: #2d3748;
    border-color: #4a5568;

    h2 {
      color: #f8f9fa;
    }

    > p {
      color: #adb5bd;
    }
  }

  .output-area {
    background: #1a202c;
    border-color: #4a5568;
  }

  .typewriter-output {
    color: #e2e8f0;

    &.code {
      background: #2d3748;
      border-color: #4a5568;
      color: #e2e8f0;
    }
  }

  .status {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  input[appTypewriter] {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;

    &::placeholder {
      color: #a0aec0;
    }

    &:focus {
      border-color: #4299e1;
      box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
  }
}
