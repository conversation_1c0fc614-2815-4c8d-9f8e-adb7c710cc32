.enhanced-logs {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  background: var(--logs-bg, #f8f9fa);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &.dark {
    background: var(--logs-bg-dark, #1e1e1e);
    color: var(--logs-text-dark, #d4d4d4);
  }
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--logs-header-bg, #fff);
  border-bottom: 1px solid var(--logs-border, #e9ecef);

  .dark & {
    background: var(--logs-header-bg-dark, #2d2d2d);
    border-bottom-color: var(--logs-border-dark, #404040);
  }

  h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--logs-title-color, #495057);

    .dark & {
      color: var(--logs-title-color-dark, #f8f9fa);
    }
  }
}

.logs-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.pause {
    background: #ffc107;
    color: #212529;

    &:hover:not(:disabled) {
      background: #e0a800;
    }
  }

  &.resume {
    background: #28a745;
    color: white;

    &:hover:not(:disabled) {
      background: #1e7e34;
    }
  }

  &.clear {
    background: #dc3545;
    color: white;

    &:hover:not(:disabled) {
      background: #c82333;
    }
  }

  &.add {
    background: #007bff;
    color: white;

    &:hover:not(:disabled) {
      background: #0056b3;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #6c757d !important;
  }
}

.logs-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.logs-status {
  padding: 0.5rem 1rem;
  background: var(--logs-status-bg, #e9ecef);
  border-bottom: 1px solid var(--logs-border, #dee2e6);
  font-size: 0.875rem;
  font-weight: 500;

  .dark & {
    background: var(--logs-status-bg-dark, #343a40);
    border-bottom-color: var(--logs-border-dark, #495057);
  }

  .status-typing {
    color: #28a745;
    font-weight: 600;
  }

  .status-paused {
    color: #ffc107;
    font-weight: 600;
  }

  .status-completed {
    color: #6c757d;
    font-weight: 600;
  }
}

.logs-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: var(--logs-content-bg, #fff);

  .dark & {
    background: var(--logs-content-bg-dark, #1e1e1e);
  }
}

.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--logs-entry-border, #f1f3f4);
  font-size: 0.875rem;
  line-height: 1.4;
  transition: background-color 0.2s ease;

  .dark & {
    border-bottom-color: var(--logs-entry-border-dark, #2d2d2d);
  }

  &.typing {
    background-color: rgba(0, 123, 255, 0.05);
    border-radius: 4px;
    padding: 0.75rem;
    margin: 0.25rem 0;
    animation: pulse 2s infinite;

    .dark & {
      background-color: rgba(66, 153, 225, 0.1);
    }
  }

  &.log-info {
    .log-level {
      color: #007bff;
      background: rgba(0, 123, 255, 0.1);
    }
  }

  &.log-success {
    .log-level {
      color: #28a745;
      background: rgba(40, 167, 69, 0.1);
    }
  }

  &.log-warn {
    .log-level {
      color: #ffc107;
      background: rgba(255, 193, 7, 0.1);
    }
  }

  &.log-error {
    .log-level {
      color: #dc3545;
      background: rgba(220, 53, 69, 0.1);
    }
  }

  &.log-debug {
    .log-level {
      color: #6c757d;
      background: rgba(108, 117, 125, 0.1);
    }
  }
}

.log-timestamp {
  flex-shrink: 0;
  color: var(--logs-timestamp-color, #6c757d);
  font-weight: 500;
  min-width: 80px;

  .dark & {
    color: var(--logs-timestamp-color-dark, #adb5bd);
  }
}

.log-level {
  flex-shrink: 0;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-weight: 600;
  font-size: 0.75rem;
  text-align: center;
  min-width: 60px;
}

.log-message {
  flex: 1;
  color: var(--logs-message-color, #495057);
  word-wrap: break-word;

  .dark & {
    color: var(--logs-message-color-dark, #e9ecef);
  }
}

.typewriter-cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: var(--logs-cursor-color, #007bff);
  margin-left: 2px;
  animation: blink 1s infinite;

  .dark & {
    background-color: var(--logs-cursor-color-dark, #4dabf7);
  }
}

.no-logs {
  text-align: center;
  color: var(--logs-empty-color, #6c757d);
  font-style: italic;
  padding: 2rem;

  .dark & {
    color: var(--logs-empty-color-dark, #adb5bd);
  }
}

// Animations
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
  }
}

// Scrollbar styling
.logs-content {
  scrollbar-width: thin;
  scrollbar-color: var(--logs-scrollbar-thumb, #c1c1c1) var(--logs-scrollbar-track, #f1f1f1);

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--logs-scrollbar-track, #f1f1f1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--logs-scrollbar-thumb, #c1c1c1);
    border-radius: 4px;

    &:hover {
      background: var(--logs-scrollbar-thumb-hover, #a8a8a8);
    }
  }

  .dark & {
    scrollbar-color: var(--logs-scrollbar-thumb-dark, #555) var(--logs-scrollbar-track-dark, #2d2d2d);

    &::-webkit-scrollbar-track {
      background: var(--logs-scrollbar-track-dark, #2d2d2d);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--logs-scrollbar-thumb-dark, #555);

      &:hover {
        background: var(--logs-scrollbar-thumb-hover-dark, #777);
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .logs-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    h3 {
      text-align: center;
    }
  }

  .logs-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .control-btn {
    flex: 1;
    min-width: 80px;
  }

  .log-entry {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .log-timestamp,
  .log-level {
    min-width: auto;
  }
}

// Accessibility
@media (prefers-reduced-motion: reduce) {
  .typewriter-cursor,
  .log-entry.typing {
    animation: none;
  }

  .typewriter-cursor {
    opacity: 1;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .enhanced-logs {
    border: 2px solid CanvasText;
  }

  .log-entry {
    border-bottom-color: CanvasText;
  }

  .log-level {
    border: 1px solid CanvasText;
  }
}
