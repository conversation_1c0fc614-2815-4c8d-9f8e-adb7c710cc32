import {
  Component,
  Input,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  TypewriterService,
  TypewriterState,
  ContentType
} from '../../services/typewriter.service';
import { SubscriptionManager } from '../../utils/subscription-management.util';

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug' | 'success';
  message: string;
  type?: 'text' | 'code' | 'json';
  metadata?: any;
}

/**
 * Enhanced logs component using the new TypewriterService
 * Demonstrates best practices for implementing typewriter effects in logs
 */
@Component({
  selector: 'app-enhanced-logs',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="enhanced-logs" [class]="theme">
      <div class="logs-header">
        <h3>Application Logs</h3>
        <div class="logs-controls">
          <button
            (click)="pauseLogs()"
            [disabled]="currentState !== 'typing'"
            class="control-btn pause">
            Pause
          </button>
          <button
            (click)="resumeLogs()"
            [disabled]="currentState !== 'paused'"
            class="control-btn resume">
            Resume
          </button>
          <button (click)="clearLogs()" class="control-btn clear">
            Clear
          </button>
          <button (click)="addSampleLog()" class="control-btn add">
            Add Log
          </button>
        </div>
      </div>

      <div class="logs-container">
        <div class="logs-status" *ngIf="currentState !== 'idle'">
          Status: <span [class]="'status-' + currentState">{{ currentState | titlecase }}</span>
        </div>

        <div class="logs-content" #logsContent>
          <div
            *ngFor="let log of displayedLogs; let i = index; trackBy: trackByLogId"
            class="log-entry"
            [class]="'log-' + log.level"
            [class.typing]="isCurrentlyTyping(i)">

            <span class="log-timestamp">{{ log.timestamp }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.visibleMessage || log.message }}</span>

            <span
              *ngIf="isCurrentlyTyping(i) && showCursor"
              class="typewriter-cursor">|</span>
          </div>

          <div *ngIf="displayedLogs.length === 0" class="no-logs">
            No logs available. Click "Add Log" to see the typewriter effect.
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./enhanced-logs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EnhancedLogsComponent implements OnInit, OnDestroy {
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() showCursor: boolean = true;
  @Input() typingSpeed: number = 40;
  @Input() autoScroll: boolean = true;

  displayedLogs: (LogEntry & { visibleMessage?: string })[] = [];
  currentState: TypewriterState = TypewriterState.IDLE;
  currentTypingIndex: number = -1;

  private logTypewriterInstance?: string;
  private subscriptionManager = new SubscriptionManager();
  private logCounter = 0;

  // Sample log messages for demonstration
  private sampleMessages = [
    { level: 'info' as const, message: 'Application started successfully' },
    { level: 'debug' as const, message: 'Loading configuration from config.json' },
    { level: 'info' as const, message: 'Database connection established' },
    { level: 'warn' as const, message: 'Deprecated API endpoint used: /api/v1/users' },
    { level: 'success' as const, message: 'User authentication completed' },
    { level: 'error' as const, message: 'Failed to connect to external service: timeout after 30s' },
    { level: 'debug' as const, message: 'Cache hit ratio: 85.3% (1,247 hits, 213 misses)' },
    { level: 'info' as const, message: 'Processing batch job: 1,500 records' },
    { level: 'success' as const, message: 'Deployment completed successfully' }
  ];

  constructor(
    private typewriterService: TypewriterService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initializeTypewriter();
  }

  ngOnDestroy(): void {
    if (this.logTypewriterInstance) {
      this.typewriterService.stop(this.logTypewriterInstance);
    }
  }

  private initializeTypewriter(): void {
    // Create a batch instance for logs
    this.logTypewriterInstance = this.typewriterService.createBatch({
      contentType: ContentType.TEXT,
      typingSpeed: this.typingSpeed,
      pauseBeforeTyping: 300,
      showCursor: false, // We handle cursor display in template
      onStateChange: (state) => {
        this.currentState = state;
        this.cdr.detectChanges();
      }
    });

    // Subscribe to text updates
    if (this.logTypewriterInstance) {
      const textObservable = this.typewriterService.getText$(this.logTypewriterInstance);
      if (textObservable) {
        this.subscriptionManager.subscribe(
          textObservable,
          (text: string) => this.handleTypewriterUpdate(text)
        );
      }

      const stateObservable = this.typewriterService.getState$(this.logTypewriterInstance);
      if (stateObservable) {
        this.subscriptionManager.subscribe(
          stateObservable,
          (state: TypewriterState) => {
            this.currentState = state;
            this.cdr.detectChanges();
          }
        );
      }
    }
  }

  private handleTypewriterUpdate(text: string): void {
    // Parse the accumulated text and update visible messages
    const lines = text.split('\n').filter(line => line.trim());

    // Update visible messages for each log entry
    lines.forEach((line, index) => {
      if (this.displayedLogs[index]) {
        this.displayedLogs[index].visibleMessage = line;
      }
    });

    // Determine which log is currently being typed
    this.currentTypingIndex = lines.length - 1;

    if (this.autoScroll) {
      this.scrollToBottom();
    }

    this.cdr.detectChanges();
  }

  addSampleLog(): void {
    const randomMessage = this.sampleMessages[Math.floor(Math.random() * this.sampleMessages.length)];
    this.addLog(randomMessage.level, randomMessage.message);
  }

  addLog(level: LogEntry['level'], message: string, type: LogEntry['type'] = 'text'): void {
    const logEntry: LogEntry & { visibleMessage?: string } = {
      id: `log-${++this.logCounter}`,
      timestamp: new Date().toLocaleTimeString(),
      level,
      message,
      type,
      visibleMessage: ''
    };

    this.displayedLogs.push(logEntry);

    // Add to typewriter service
    if (this.logTypewriterInstance) {
      const formattedMessage = `[${logEntry.timestamp}] ${logEntry.level.toUpperCase()}: ${logEntry.message}`;
      this.typewriterService.addText(this.logTypewriterInstance, formattedMessage);
    }

    this.cdr.detectChanges();
  }

  pauseLogs(): void {
    if (this.logTypewriterInstance) {
      this.typewriterService.pause(this.logTypewriterInstance);
    }
  }

  resumeLogs(): void {
    if (this.logTypewriterInstance) {
      this.typewriterService.resume(this.logTypewriterInstance);
    }
  }

  clearLogs(): void {
    if (this.logTypewriterInstance) {
      this.typewriterService.stop(this.logTypewriterInstance);
    }

    this.displayedLogs = [];
    this.currentTypingIndex = -1;
    this.logCounter = 0;

    // Reinitialize typewriter
    this.initializeTypewriter();
    this.cdr.detectChanges();
  }

  isCurrentlyTyping(index: number): boolean {
    return this.currentState === TypewriterState.TYPING && index === this.currentTypingIndex;
  }

  trackByLogId(_index: number, log: LogEntry): string {
    return log.id;
  }

  private scrollToBottom(): void {
    setTimeout(() => {
      const container = document.querySelector('.logs-content');
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }, 0);
  }

  // Bulk add logs for demonstration
  addBulkLogs(): void {
    const messages = [
      { level: 'info' as const, message: 'Starting bulk operation...' },
      { level: 'debug' as const, message: 'Validating input parameters' },
      { level: 'info' as const, message: 'Processing 1000 records' },
      { level: 'success' as const, message: 'Bulk operation completed successfully' }
    ];

    messages.forEach((msg, index) => {
      setTimeout(() => {
        this.addLog(msg.level, msg.message);
      }, index * 500);
    });
  }
}
