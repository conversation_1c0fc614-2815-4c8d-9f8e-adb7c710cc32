import { 
  Directive, 
  ElementRef, 
  Input, 
  OnInit, 
  OnDestroy, 
  Output, 
  EventEmitter,
  Renderer2
} from '@angular/core';
import { 
  TypewriterService, 
  TypewriterMode, 
  TypewriterState, 
  ContentType,
  TypewriterConfig 
} from '../services/typewriter.service';
import { SubscriptionManager } from '../utils/subscription-manager';

/**
 * Directive for adding typewriter effects to any element
 * 
 * Usage examples:
 * 
 * Single text:
 * <div appTypewriter="Hello World!" [typingSpeed]="50"></div>
 * 
 * Multiple texts (cycling):
 * <div appTypewriter 
 *      [texts]="['Text 1', 'Text 2', 'Text 3']" 
 *      [mode]="'cycling'">
 * </div>
 * 
 * With configuration:
 * <div appTypewriter 
 *      [texts]="myTexts"
 *      [mode]="'sequence'"
 *      [contentType]="'code'"
 *      [showCursor]="true"
 *      (onComplete)="handleComplete()"
 *      (onStateChange)="handleStateChange($event)">
 * </div>
 */
@Directive({
  selector: '[appTypewriter]'
})
export class TypewriterDirective implements OnInit, OnDestroy {
  @Input('appTypewriter') singleText?: string;
  @Input() texts: string[] = [];
  @Input() mode: TypewriterMode = TypewriterMode.SINGLE;
  @Input() typingSpeed?: number;
  @Input() erasingSpeed?: number;
  @Input() pauseBeforeErasing?: number;
  @Input() pauseBeforeTyping?: number;
  @Input() staticText?: string;
  @Input() contentType?: ContentType;
  @Input() shuffle?: boolean;
  @Input() showCursor?: boolean;
  @Input() cursorChar?: string;
  @Input() preserveWhitespace?: boolean;
  @Input() autoStart: boolean = true;
  @Input() targetAttribute: string = 'textContent'; // 'textContent', 'innerHTML', 'placeholder'

  @Output() onComplete = new EventEmitter<void>();
  @Output() onTextComplete = new EventEmitter<{ text: string; index: number }>();
  @Output() onStateChange = new EventEmitter<TypewriterState>();

  private instanceId?: string;
  private subscriptionManager = new SubscriptionManager();

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private typewriterService: TypewriterService
  ) {}

  ngOnInit(): void {
    if (this.autoStart) {
      this.startAnimation();
    }
  }

  ngOnDestroy(): void {
    this.stopAnimation();
    this.subscriptionManager.unsubscribeAll();
  }

  /**
   * Start the typewriter animation
   */
  startAnimation(): void {
    this.stopAnimation(); // Clean up any existing animation

    const textsToUse = this.getTextsToAnimate();
    if (textsToUse.length === 0) {
      return;
    }

    const config: Partial<TypewriterConfig> = {
      mode: this.mode,
      typingSpeed: this.typingSpeed,
      erasingSpeed: this.erasingSpeed,
      pauseBeforeErasing: this.pauseBeforeErasing,
      pauseBeforeTyping: this.pauseBeforeTyping,
      staticText: this.staticText,
      contentType: this.contentType,
      shuffle: this.shuffle,
      showCursor: this.showCursor,
      cursorChar: this.cursorChar,
      preserveWhitespace: this.preserveWhitespace,
      onComplete: () => this.onComplete.emit(),
      onTextComplete: (text: string, index: number) => 
        this.onTextComplete.emit({ text, index }),
      onStateChange: (state: TypewriterState) => 
        this.onStateChange.emit(state)
    };

    this.instanceId = this.typewriterService.createInstance(textsToUse, config);

    // Subscribe to text updates
    const textObservable = this.typewriterService.getText$(this.instanceId);
    if (textObservable) {
      this.subscriptionManager.subscribe(
        textObservable,
        (text: string) => this.updateElementText(text)
      );
    }

    this.typewriterService.start(this.instanceId);
  }

  /**
   * Stop the typewriter animation
   */
  stopAnimation(): void {
    if (this.instanceId) {
      this.typewriterService.stop(this.instanceId);
      this.instanceId = undefined;
    }
  }

  /**
   * Pause the animation
   */
  pauseAnimation(): void {
    if (this.instanceId) {
      this.typewriterService.pause(this.instanceId);
    }
  }

  /**
   * Resume the animation
   */
  resumeAnimation(): void {
    if (this.instanceId) {
      this.typewriterService.resume(this.instanceId);
    }
  }

  /**
   * Add text to the animation (for batch mode)
   */
  addText(text: string): void {
    if (this.instanceId) {
      this.typewriterService.addText(this.instanceId, text);
    }
  }

  /**
   * Get the current state
   */
  getCurrentState(): TypewriterState {
    if (this.instanceId) {
      return this.typewriterService.getCurrentState(this.instanceId);
    }
    return TypewriterState.IDLE;
  }

  /**
   * Get the current visible text
   */
  getCurrentText(): string {
    if (this.instanceId) {
      return this.typewriterService.getCurrentText(this.instanceId);
    }
    return '';
  }

  private getTextsToAnimate(): string[] {
    if (this.singleText) {
      return [this.singleText];
    }
    return this.texts || [];
  }

  private updateElementText(text: string): void {
    const element = this.elementRef.nativeElement;
    
    switch (this.targetAttribute) {
      case 'innerHTML':
        this.renderer.setProperty(element, 'innerHTML', text);
        break;
      case 'placeholder':
        this.renderer.setAttribute(element, 'placeholder', text);
        break;
      case 'textContent':
      default:
        this.renderer.setProperty(element, 'textContent', text);
        break;
    }
  }
}
