import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withPreloading } from '@angular/router';
import { provideMarkdown } from 'ngx-markdown';
import { MsalModule, MsalService, MSAL_INSTANCE } from '@azure/msal-angular';
import { IPublicClientApplication, PublicClientApplication } from '@azure/msal-browser';
import { msalConfig } from './config/auth.config';
import { provideAnimations } from '@angular/platform-browser/animations';
import { LOGGER_CONFIG, LoggerService } from './shared/utils/logger';
import { environment } from '../environments/environment';
import { CacheInterceptor } from './shared/interceptors/cache.interceptor';
import { HttpErrorInterceptor } from './shared/interceptors/http-error.interceptor';
import { SelectivePreloadingStrategy } from './shared/strategies/selective-preloading-strategy';

import { routes } from './app.routes';

function MSALInstanceFactory(): IPublicClientApplication {
  return new PublicClientApplication(msalConfig);
}

export const appConfig: ApplicationConfig = {
  providers: [
    // Configure HTTP client with interceptors
    // Order matters: HttpErrorInterceptor should run first to catch all errors
    provideHttpClient(
      withInterceptors([HttpErrorInterceptor, CacheInterceptor])
    ),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(
      routes,
      withPreloading(SelectivePreloadingStrategy) 
    ),
    SelectivePreloadingStrategy, 
    provideMarkdown(),

    provideAnimations(), // Add animations provider
    importProvidersFrom(MsalModule),
    {
      provide: MSAL_INSTANCE,
      useFactory: MSALInstanceFactory,
    },
    MsalService,

    // Logger configuration
    LoggerService,
    {
      provide: LOGGER_CONFIG,
      useValue: {
        enableLogging: !environment.production,
        logLevel: environment.production ? 'error' : 'debug',
        enableConsoleColors: true,
      },
    },
  ],
};
