import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  TypewriterService, 
  TypewriterMode, 
  TypewriterState, 
  ContentType 
} from '../../shared/services/typewriter.service';
import { TypewriterDirective } from '../../shared/directives/typewriter.directive';
import { TypewriterComponent } from '../../shared/components/typewriter/typewriter.component';
import { EnhancedLogsComponent } from '../../shared/components/enhanced-logs/enhanced-logs.component';

/**
 * Test component to demonstrate all typewriter functionality
 */
@Component({
  selector: 'app-typewriter-test',
  standalone: true,
  imports: [CommonModule, TypewriterDirective, TypewriterComponent, EnhancedLogsComponent],
  template: `
    <div class="typewriter-test-container">
      <h1>Enhanced Typewriter Service Test</h1>
      <p class="subtitle">Testing all typewriter functionality across different components</p>

      <!-- Basic Service Usage -->
      <section class="test-section">
        <h2>1. Basic Service Usage</h2>
        
        <div class="test-group">
          <h3>Single Text Animation</h3>
          <div class="output-box">{{ singleText }}</div>
          <button (click)="testSingleText()" class="test-btn">Start Single Text</button>
        </div>

        <div class="test-group">
          <h3>Cycling Text Animation</h3>
          <div class="output-box">{{ cyclingText }}</div>
          <button (click)="testCyclingText()" class="test-btn">Start Cycling Text</button>
        </div>

        <div class="test-group">
          <h3>Sequence Text Animation</h3>
          <div class="output-box sequence">{{ sequenceText }}</div>
          <button (click)="testSequenceText()" class="test-btn">Start Sequence</button>
        </div>
      </section>

      <!-- Directive Usage -->
      <section class="test-section">
        <h2>2. Typewriter Directive</h2>
        
        <div class="test-group">
          <h3>Text Content</h3>
          <div class="output-box" 
               appTypewriter
               [texts]="['Directive is working!', 'This is amazing!', 'So easy to use!']"
               [mode]="TypewriterMode.CYCLING"
               [typingSpeed]="50"
               [showCursor]="true">
          </div>
        </div>

        <div class="test-group">
          <h3>Input Placeholder</h3>
          <input type="text" 
                 class="test-input"
                 appTypewriter
                 [texts]="['Enter your name...', 'Type something...', 'What\\'s on your mind?']"
                 [mode]="TypewriterMode.CYCLING"
                 [targetAttribute]="'placeholder'"
                 [typingSpeed]="60">
        </div>
      </section>

      <!-- Component Usage -->
      <section class="test-section">
        <h2>3. Typewriter Component</h2>
        
        <div class="test-group">
          <h3>Component with Cursor</h3>
          <app-typewriter 
            [texts]="['Component Demo', 'Easy to use!', 'Highly customizable']"
            [mode]="TypewriterMode.CYCLING"
            [showCursor]="true"
            [cursorChar]="'_'"
            [typingSpeed]="40"
            theme="light">
          </app-typewriter>
        </div>
      </section>

      <!-- Batch/Logs Demo -->
      <section class="test-section">
        <h2>4. Batch Mode (Logs)</h2>
        
        <div class="test-group">
          <h3>Dynamic Log Streaming</h3>
          <div class="logs-container">
            <div class="logs-output">{{ batchText }}</div>
            <div class="logs-controls">
              <button (click)="addBatchText()" class="test-btn">Add Log Entry</button>
              <button (click)="clearBatch()" class="test-btn secondary">Clear Logs</button>
            </div>
          </div>
        </div>
      </section>

      <!-- Enhanced Logs Component -->
      <section class="test-section">
        <h2>5. Enhanced Logs Component</h2>
        <div class="enhanced-logs-demo">
          <app-enhanced-logs 
            [theme]="'light'"
            [showCursor]="true"
            [typingSpeed]="30">
          </app-enhanced-logs>
        </div>
      </section>

      <!-- Content Type Tests -->
      <section class="test-section">
        <h2>6. Content Type Adaptive Speeds</h2>
        
        <div class="test-group">
          <h3>Code Content (Fast)</h3>
          <div class="output-box code">{{ codeText }}</div>
          <button (click)="testCodeContent()" class="test-btn">Start Code Animation</button>
        </div>

        <div class="test-group">
          <h3>Markdown Content (Medium)</h3>
          <div class="output-box markdown">{{ markdownText }}</div>
          <button (click)="testMarkdownContent()" class="test-btn">Start Markdown Animation</button>
        </div>
      </section>

      <!-- Control Tests -->
      <section class="test-section">
        <h2>7. Animation Controls</h2>
        
        <div class="test-group">
          <h3>Pause/Resume Test</h3>
          <div class="output-box">{{ controlText }}</div>
          <div class="control-buttons">
            <button (click)="startControlTest()" class="test-btn">Start</button>
            <button (click)="pauseControlTest()" class="test-btn">Pause</button>
            <button (click)="resumeControlTest()" class="test-btn">Resume</button>
            <button (click)="stopControlTest()" class="test-btn secondary">Stop</button>
          </div>
          <div class="status">Status: {{ controlState }}</div>
        </div>
      </section>
    </div>
  `,
  styleUrls: ['./typewriter-test.component.scss']
})
export class TypewriterTestComponent implements OnInit, OnDestroy {
  // Expose enums to template
  TypewriterMode = TypewriterMode;
  TypewriterState = TypewriterState;

  // Display properties
  singleText = '';
  cyclingText = '';
  sequenceText = '';
  batchText = '';
  codeText = '';
  markdownText = '';
  controlText = '';
  controlState = 'idle';

  // Instance tracking
  private instances: { [key: string]: string } = {};
  private batchInstance?: string;
  private controlInstance?: string;

  // Sample content
  private sampleTexts = [
    'Welcome to the Enhanced Typewriter Service!',
    'This service provides multiple animation modes.',
    'It supports adaptive typing speeds.',
    'Perfect for creating engaging user experiences!'
  ];

  private sampleCode = `function greetUser(name) {
  console.log(\`Hello, \${name}!\`);
  return true;
}`;

  private sampleMarkdown = `# Enhanced Typewriter

This is a **powerful** typewriter service that supports:

- Multiple animation modes
- Adaptive typing speeds
- Reactive state management
- Easy integration`;

  constructor(private typewriterService: TypewriterService) {}

  ngOnInit(): void {
    // Initialize batch instance
    this.batchInstance = this.typewriterService.createBatch({
      contentType: ContentType.TEXT,
      typingSpeed: 40,
      pauseBeforeTyping: 300
    });

    if (this.batchInstance) {
      this.typewriterService.getText$(this.batchInstance)?.subscribe(text => {
        this.batchText = text;
      });
    }
  }

  ngOnDestroy(): void {
    // Clean up all instances
    Object.values(this.instances).forEach(id => {
      this.typewriterService.stop(id);
    });
    
    if (this.batchInstance) {
      this.typewriterService.stop(this.batchInstance);
    }
    
    if (this.controlInstance) {
      this.typewriterService.stop(this.controlInstance);
    }
  }

  testSingleText(): void {
    this.stopInstance('single');
    this.singleText = '';
    
    const instanceId = this.typewriterService.typeText(this.sampleTexts[0], {
      typingSpeed: 50,
      showCursor: false
    });
    
    this.instances['single'] = instanceId;
    this.typewriterService.getText$(instanceId)?.subscribe(text => {
      this.singleText = text;
    });
  }

  testCyclingText(): void {
    this.stopInstance('cycling');
    this.cyclingText = '';
    
    const instanceId = this.typewriterService.cycleTexts(this.sampleTexts, {
      typingSpeed: 60,
      erasingSpeed: 40,
      pauseBeforeErasing: 2000,
      pauseBeforeTyping: 500
    });
    
    this.instances['cycling'] = instanceId;
    this.typewriterService.getText$(instanceId)?.subscribe(text => {
      this.cyclingText = text;
    });
  }

  testSequenceText(): void {
    this.stopInstance('sequence');
    this.sequenceText = '';
    
    const instanceId = this.typewriterService.sequenceTexts(this.sampleTexts, {
      typingSpeed: 40,
      pauseBeforeTyping: 800
    });
    
    this.instances['sequence'] = instanceId;
    this.typewriterService.getText$(instanceId)?.subscribe(text => {
      this.sequenceText = text;
    });
  }

  testCodeContent(): void {
    this.stopInstance('code');
    this.codeText = '';
    
    const instanceId = this.typewriterService.typeText(this.sampleCode, {
      contentType: ContentType.CODE,
      typingSpeed: 30,
      showCursor: false
    });
    
    this.instances['code'] = instanceId;
    this.typewriterService.getText$(instanceId)?.subscribe(text => {
      this.codeText = text;
    });
  }

  testMarkdownContent(): void {
    this.stopInstance('markdown');
    this.markdownText = '';
    
    const instanceId = this.typewriterService.typeText(this.sampleMarkdown, {
      contentType: ContentType.MARKDOWN,
      typingSpeed: 40,
      showCursor: false
    });
    
    this.instances['markdown'] = instanceId;
    this.typewriterService.getText$(instanceId)?.subscribe(text => {
      this.markdownText = text;
    });
  }

  addBatchText(): void {
    if (this.batchInstance) {
      const logMessages = [
        '[INFO] Application started successfully',
        '[DEBUG] Loading configuration files...',
        '[INFO] Database connection established',
        '[WARN] Deprecated API endpoint used',
        '[SUCCESS] User authentication completed',
        '[ERROR] Failed to connect to external service',
        '[DEBUG] Cache hit ratio: 85.3%',
        '[INFO] Processing batch job: 1,500 records'
      ];
      
      const randomMessage = logMessages[Math.floor(Math.random() * logMessages.length)];
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = `[${timestamp}] ${randomMessage}`;
      
      this.typewriterService.addText(this.batchInstance, logEntry);
    }
  }

  clearBatch(): void {
    if (this.batchInstance) {
      this.typewriterService.stop(this.batchInstance);
      this.batchText = '';
      
      // Recreate batch instance
      this.batchInstance = this.typewriterService.createBatch({
        contentType: ContentType.TEXT,
        typingSpeed: 40,
        pauseBeforeTyping: 300
      });
      
      if (this.batchInstance) {
        this.typewriterService.getText$(this.batchInstance)?.subscribe(text => {
          this.batchText = text;
        });
      }
    }
  }

  startControlTest(): void {
    this.stopControlTest();
    this.controlText = '';
    this.controlState = 'typing';
    
    this.controlInstance = this.typewriterService.typeText(
      'This is a long text that you can pause and resume to test the control functionality of the enhanced typewriter service.',
      {
        typingSpeed: 80, // Slower for better control testing
        showCursor: false,
        onStateChange: (state) => {
          this.controlState = state;
        }
      }
    );
    
    if (this.controlInstance) {
      this.typewriterService.getText$(this.controlInstance)?.subscribe(text => {
        this.controlText = text;
      });
    }
  }

  pauseControlTest(): void {
    if (this.controlInstance) {
      this.typewriterService.pause(this.controlInstance);
    }
  }

  resumeControlTest(): void {
    if (this.controlInstance) {
      this.typewriterService.resume(this.controlInstance);
    }
  }

  stopControlTest(): void {
    if (this.controlInstance) {
      this.typewriterService.stop(this.controlInstance);
      this.controlInstance = undefined;
      this.controlText = '';
      this.controlState = 'idle';
    }
  }

  private stopInstance(key: string): void {
    if (this.instances[key]) {
      this.typewriterService.stop(this.instances[key]);
      delete this.instances[key];
    }
  }
}
