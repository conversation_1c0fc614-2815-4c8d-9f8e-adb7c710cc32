.typewriter-test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background: #f8f9fa;
  min-height: 100vh;

  h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
    font-weight: 300;
  }

  .subtitle {
    text-align: center;
    color: #6c757d;
    font-size: 1.1rem;
    margin-bottom: 3rem;
  }
}

.test-section {
  margin-bottom: 3rem;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;

  h2 {
    color: #495057;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 500;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
  }

  h3 {
    color: #6c757d;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

.test-group {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;

  &:last-child {
    margin-bottom: 0;
  }
}

.output-box {
  min-height: 60px;
  padding: 1rem;
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  white-space: pre-wrap;
  word-wrap: break-word;
  position: relative;

  &.sequence {
    min-height: 120px;
  }

  &.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #2d3748;
    color: #e2e8f0;
    font-size: 0.9rem;
    white-space: pre;
    overflow-x: auto;
  }

  &.markdown {
    line-height: 1.6;
  }

  &:empty::before {
    content: 'Waiting for animation...';
    color: #adb5bd;
    font-style: italic;
  }
}

.test-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #007bff;
  color: white;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;

  &:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  }

  &.secondary {
    background: #6c757d;

    &:hover {
      background: #545b62;
    }
  }
}

.test-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
}

.logs-container {
  .logs-output {
    min-height: 120px;
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem;
    background: #1e1e1e;
    color: #d4d4d4;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    white-space: pre-line;
    margin-bottom: 1rem;

    &:empty::before {
      content: 'No logs yet. Click "Add Log Entry" to see the typewriter effect.';
      color: #6c757d;
      font-style: italic;
    }
  }

  .logs-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

.enhanced-logs-demo {
  height: 400px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.control-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.status {
  padding: 0.5rem 1rem;
  background: #e9ecef;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
  display: inline-block;

  &::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
    background: #6c757d;
  }

  &:has-text("typing")::before {
    background: #28a745;
    animation: pulse 1s infinite;
  }

  &:has-text("paused")::before {
    background: #ffc107;
  }

  &:has-text("completed")::before {
    background: #17a2b8;
  }
}

// Scrollbar styling
.logs-output {
  scrollbar-width: thin;
  scrollbar-color: #555 #2d2d2d;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #2d2d2d;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;

    &:hover {
      background: #777;
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Responsive design
@media (max-width: 768px) {
  .typewriter-test-container {
    padding: 1rem;

    h1 {
      font-size: 2rem;
    }
  }

  .test-section {
    padding: 1.5rem;
  }

  .test-group {
    padding: 1rem;
  }

  .control-buttons,
  .logs-controls {
    flex-direction: column;

    .test-btn {
      margin-right: 0;
      width: 100%;
    }
  }

  .enhanced-logs-demo {
    height: 300px;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .typewriter-test-container {
    background: #1a1a1a;
    color: #e9ecef;

    h1 {
      color: #f8f9fa;
    }

    .subtitle {
      color: #adb5bd;
    }
  }

  .test-section {
    background: #2d3748;
    border-color: #4a5568;

    h2 {
      color: #f8f9fa;
      border-color: #4299e1;
    }

    h3 {
      color: #adb5bd;
    }
  }

  .test-group {
    background: #1a202c;
    border-color: #4a5568;
  }

  .output-box {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;

    &:empty::before {
      color: #718096;
    }
  }

  .test-input {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;

    &::placeholder {
      color: #a0aec0;
    }

    &:focus {
      border-color: #4299e1;
      box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
  }

  .status {
    background: #4a5568;
    color: #e2e8f0;
  }
}
