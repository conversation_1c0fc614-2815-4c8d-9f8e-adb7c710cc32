<div id="prompt-content-container">
  <!-- First Screen: Prompt Content Container -->
  <section id="prompt-section-container">
    <div class="container-fluid">
      <app-hero-section-header
        [headerTitle]="'Dream Build Launch!'"
        [headerDescription]="'<PERSON><PERSON><PERSON><PERSON> blends Art Science to bring your ideas to life.'"
        [subHeading]="'What would you like to build today?'"></app-hero-section-header>

      <div id="prompt-bar-container" class="container-fluid px-2">
        <div class="row mt-2 d-flex align-items-center justify-content-center">
          <div class="col-12 d-flex justify-content-center">
            <awe-prompt-bar
              [theme]="theme"
              (enterPressed)="handleEnterPressed()"
              [(textValue)]="currentPrompt"
              (ngModelChange)="handlePromptChange($event)"
              (iconClicked)="handleIconClick($event)"
              [theme]="theme"
              (fileRemoved)="onFileRemoved($event)"
              (filePreviewClosed)="closePreview()"
              (enterPressed)="handleEnterPressed()"
              [class.disabled-prompt-bar]="isEnhancing || isGenerating"
              [class.processing]="isEnhancing || isGenerating"
              appTypewriterPlaceholder
              [texts]="animatedTexts">
              <div class="custom-content">
                <!-- Selected Files Display -->
                <div class="mb-2 selected-files" *ngIf="selectedFiles.length > 0">
                  <div class="py-1 px-2 file-item" *ngFor="let file of selectedFiles">
                    <div class="file-preview" (click)="showFilePreview(file)">
                      <img
                        class="mr-2 file-preview-image previewable-image"
                        [src]="file.url"
                        [alt]="file.name"
                        title="Preview image"
                        loading="lazy"
                        decoding="async" />
                      <span class="file-name">{{ truncateFileName(file.name) }}</span>
                    </div>
                    <awe-icons
                      iconName="awe_close"
                      (click)="removeFile(file.id)"
                      class="pt-2"
                      role="button"
                      tabindex="0"
                      iconColor="blue"
                      [attr.aria-label]="'Remove ' + file.name"></awe-icons>
                  </div>
                </div>

                <div class="d-flex align-items-center justify-content-between pe-2 tools-container">
                  <div class="d-flex align-items-center gap-2 me-3 pills-container">
                    <awe-file-attach-pill
                      [options]="fileOptions"
                      (optionSelected)="onFileOptionSelected($event)"
                      [class.disabled]="
                        isFileAttachDisabled || isEnhancing || isGenerating
                      "></awe-file-attach-pill>
                    <awe-icon-pill
                      [options]="techOptions"
                      [selectedOption]="selectedTech"
                      [class.disabled]="isEnhancing || isGenerating"
                      (selectionChange)="onTechSelected($event)"></awe-icon-pill>
                    <awe-icon-pill
                      [options]="designOptions"
                      [selectedOption]="selectedDesign"
                      [class.disabled]="isEnhancing || isGenerating"
                      (selectionChange)="onDesignSelected($event)"></awe-icon-pill>
                  </div>
                  <div class="d-flex align-items-center gap-3 enhance-icons">
                    <!-- Show loading spinner when enhancing, otherwise show enhance icon -->
                    <ng-container *ngIf="!isEnhancing; else loadingEnhance">
                      <awe-icons
                        iconName="awe_enhance"
                        (click)="handleEnhanceText()"
                        role="button"
                        tabindex="0"
                        [attr.aria-label]="'Enhance'"
                        [class.disabled]="
                          !currentPrompt ||
                          currentPrompt.trim() === '' ||
                          enhanceClickCount >= maxEnhanceClicks
                        "
                        [style.cursor]="
                          !currentPrompt ||
                          currentPrompt.trim() === '' ||
                          enhanceClickCount >= maxEnhanceClicks
                            ? 'not-allowed'
                            : 'pointer'
                        "
                        [color]="getIconColor()"></awe-icons>
                    </ng-container>
                    <ng-template #loadingEnhance>
                      <div class="d-flex align-items-center justify-content-center loading-spinner">
                        <div class="spinner"></div>
                      </div>
                    </ng-template>
                    <awe-icons
                      iconName="awe_enhanced_send"
                      (click)="handleEnhancedSend()"
                      class="cursor-pointer"
                      role="button"
                      tabindex="0"
                      [attr.aria-label]="'Enhanced Send'"
                      [class.disabled]="
                        !currentPrompt || currentPrompt.trim() === '' || isEnhancing
                      "
                      [style.cursor]="
                        !currentPrompt || currentPrompt.trim() === '' || isEnhancing
                          ? 'not-allowed'
                          : 'pointer'
                      "
                      [color]="getIconColor()"></awe-icons>
                  </div>
                </div>
              </div>
            </awe-prompt-bar>
          </div>
        </div>
      </div>
      <!-- Suggestion Buttons -->
      <div id="suggestions-container" class="px-2">
        <div
          class="row mt-5 d-flex justify-content-center align-items-center g-2"
          [class.invisible]="currentPrompt && currentPrompt.trim() !== ''">
          <div class="col-12 col-sm-auto text-center mb-2" *ngFor="let button of buttons">
            <awe-button
              [label]="button.label"
              [variant]="button.variant"
              [loadingType]="button.loadingType"
              [animation]="button.buttonAnimation"
              (click)="handleSuggestionClick(button.label)">
            </awe-button>
          </div>
        </div>
      </div>

      <!-- Divider Section -->
      <!-- <div id="divider-section-container">
        <div class="d-flex align-items-center justify-content-center">
          <img
            [src]="'/assets/icons/' + (theme === 'dark' ? 'divider-dark.svg' : 'divider-light.svg')"
            class="divider-image"
            tabindex="0"
            alt="divider" />
        </div>
      </div> -->
    </div>
  </section>

  <!-- Second Screen: Projects Section removed and moved to landing page -->
</div>

<!-- Simple Full Screen Image Preview Overlay -->
<div class="image-overlay" *ngIf="showPreview && previewFile">
  <awe-icons
    iconName="awe_close"
    (click)="closePreview()"
    role="button"
    tabindex="0"
    [attr.aria-label]="'Close preview'"></awe-icons>
  <img
    [src]="previewFile.url"
    [alt]="previewFile.name"
    loading="eager"
    decoding="async"
    fetchpriority="high" />
</div>
