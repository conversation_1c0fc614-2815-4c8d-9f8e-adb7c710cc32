<div class="container-fluid p-3" (click)="closeAllDropdowns()">
  <div class="row g-3">
    <div *ngFor="let section of sections" class="col-12 col-sm-6 col-lg-3">
      <!-- Section Header -->
      <div class="section-header text-center text-white p-2 rounded-top" [id]="section.id + '-header'">
        <div type="bold" class="section-title">
          <div class="topography">
            {{ section.title }}
          </div>
        </div>



        <!-- <div class="section-subtitle small text-uppercase ls-1">{{ section.subtitle }}</div> -->
      </div>

      <div class="section-second-header text-center mb-3 text-white rounded-top" [id]="section.id + '-header'">
        <div type="bold" class="secons-section-title">
          <div class="second-topography small text-uppercase ls-1">{{ section.subtitle.toLocaleUpperCase() }}</div>
        </div>
      </div>

      <!-- Drop Zone -->
      <div cdkDropList [id]="section.id" [cdkDropListData]="section.features" [cdkDropListConnectedTo]="getSectionIds()"
        (cdkDropListDropped)="onDrop($event)"
        class="feature-list bg-light border border-top-0 p-2 d-flex flex-column gap-3">
        <!-- Feature Cards -->
        <div *ngFor="let feature of section.features"
          class="feature-card bg-white shadow-sm rounded p-3 position-relative" cdkDrag
          
          (cdkDragStarted)="$event.source.element.nativeElement.style.cursor = 'grabbing'"
          (cdkDragEnded)="$event.source.element.nativeElement.style.cursor = 'grab'">
          <!-- Card Header -->
          <div class="d-flex justify-content-end align-items-end mb-2">
            <awe-heading variant="s2" type="bold" class="fw-semibold mb-0 flex-grow-1 pe-2">{{ feature.title }}</awe-heading>
            <div class="dropdown position-relative">
              <button class="btn btn-link p-0 text-dark" type="button" (click)="toggleDropdown(feature.id, $event)">
                <img [src]="threeDotsIcon" alt="menu">
              </button>
              <div class="dropdown-menu dropdown-menu-end" [class.show]="isDropdownOpen(feature.id)">
                <button class="dropdown-item border-buttom" type="button" (click)="openEditDialog(feature); closeAllDropdowns()">Edit</button>
                <button class="dropdown-item text-danger" type="button" (click)="deleteFeature(section.id, feature.id); closeAllDropdowns()">
                  Delete
                </button>
              </div>
            </div>
          </div>

          <!-- Card Description -->
          <p class="text-muted small mb-3 flex-grow-1">{{ feature.description }}</p>

          <!-- Card Tags -->
          <div class="d-flex flex-wrap gap-1">
            <span *ngFor="let tag of feature.tags" class="badge bg-secondary-subtle text-dark rounded-pill px-2 py-1"
              style="font-size: 0.75rem;">
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="section.features.length === 0" class="text-center text-muted fst-italic py-4">
          Drag and drop features here
        </div>
      </div>

      <!-- Add More Button -->
      <div class="add-more-button">
        <button (click)="addNewFeature(section.id)">
          Add more  <span class="plus-icon">+</span>
        </button>
      </div>

    </div>
  </div>
</div>

<!-- Edit Dialog Component -->
<app-edit-dialog
  [isVisible]="isDialogVisible"
  [config]="dialogConfig"
  [dropdownItems]="dropdownItems"
  (closeDialog)="closeDialog()"
  (dropdownAction)="onDialogDropdownAction($event)">

  <!-- Dialog Content -->
  <div *ngIf="currentEditingFeature" class="dialog-content">
    <!-- Title Field -->
    <div class="mb-3">
      <label for="featureTitle" class="form-label fw-semibold">Title :</label>
      <input
        type="text"
        id="featureTitle"
        class="form-control"
        [value]="currentEditingFeature.title"
        placeholder="Enter feature title">
    </div>

    <!-- Description Field -->
    <div class="mb-3">
      <label for="featureDescription" class="form-label fw-semibold">Description :</label>
      <textarea
        id="featureDescription"
        class="form-control"
        rows="4"
        [value]="currentEditingFeature.description"
        placeholder="Enter feature description"></textarea>
    </div>

    <!-- Regenerate Section -->
    <div class="mb-3">
      <label class="form-label fw-semibold">Regenerate</label>
      <div class="input-group">
        <input
          type="text"
          class="form-control"
          placeholder="Type your prompt here...">
        <button class="btn btn-outline-secondary" type="button">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>

    <!-- Update Button -->
    <div class="d-flex justify-content-end">
      <button type="button" class="btn btn-secondary px-4">Update</button>
    </div>
  </div>
</app-edit-dialog>