// Minimal custom SCSS - most styling handled by Bootstrap classes
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@900&display=swap');

// Letter spacing utility (Bootstrap doesn't have this)
.ls-1 {
  letter-spacing: 1px;
}

.section-header {
  // min-width: 369px;
  height: 100px;


  .section-title {
    // position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    .topography {
        color: #FFF;
        font-family: 'Inter', sans-serif;
        font-size: 96px;
        font-style: normal;
        font-weight: 1000;
        /* Already bold, keep this */
        line-height: 160%;
        text-transform: uppercase;
        /* Ensure letters like "S" appear strong */
        letter-spacing: -2px;
        /* Optional: tighten spacing like in image */
    }


  }

}

.section-second-header {
  height: 48px;
  min-width: 369px;

  .secons-section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: #fff;

    .second-topography {
            overflow: hidden;
            color: #808080;
            text-align: center;
            text-overflow: ellipsis;
            font-family: Inter;
            font-size: 32px;
            font-style: normal;
            font-weight: 700;
            line-height: 150%;
            /* 48px */
            text-transform: uppercase;
        }


  }

}

// Section-specific header colors (matching Figma design)
#must-have-header {
  background-color: #808080 !important;
}

#should-have-header {
  background-color: #707070 !important;
}

#could-have-header {
  background-color: #606060 !important;
}

#wont-have-header {
  background-color: #a0a0a0 !important;
}

// Section-specific button colors
.add-more-button {
  background: #989898;
  display: flex;
  height: 48px;
  padding: 8px 133px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  align-self: stretch;

  button {
    // background: #989898;
    color: #FFF;
    font-family: Mulish;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;

    span {
      color: #FFF;
      font-family: Mulish;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
}

// CDK Drag and Drop animations - Smoother
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(2deg);
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  transition: all 0.3s ease;
}

.cdk-drag-animating {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.feature-list.cdk-drop-list-dragging .feature-card:not(.cdk-drag-placeholder) {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

// Card hover effects
.feature-card {
  // transition: all 0.2s ease, cursor 0.1s ease;
  background: #FBFBFB;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.25);
  min-height: 185px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  }

  &:active {
    cursor: grabbing !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2) !important;
  }
}

button {
  min-width: 0px;
  border: none;
  background: none;
}

.border-buttom {
  border-bottom: 1px solid #303233;
}

// Custom dropdown styling
.dropdown {
  position: relative;
  height: 44px;

  .dropdown-menu {
    display: none;
    position: absolute;
    top: 35%;
    right: 70%;
    min-width: 120px;
    background-color: #fff;
    // border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    // padding: 0.5rem 0;
    z-index: 1000;

    &.show {
      display: block;
    }

    .dropdown-item {
      display: block;
      width: 100%;
      // border-bottom: 1px solid #303233;
      // padding: 0.375rem 1rem;
      clear: both;
      font-weight: 400;
      color: #212529;
      text-align: inherit;
      text-decoration: none;
      white-space: nowrap;
      background-color: transparent;
      // border: 0;
      transition: background-color 0.15s ease-in-out;

      &:hover,
      &:focus {
        background-color: #f8f9fa;
        color: #1e2125;
      }

      &.text-danger:hover {
        background-color: #f5c2c7;
        color: #842029;
      }
    }
  }
}