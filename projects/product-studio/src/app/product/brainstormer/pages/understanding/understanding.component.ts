import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  ButtonComponent,
  CaptionComponent,
  HeaderComponent,
  HeadingComponent,
} from '@awe/play-comp-library';

export interface CanvasItem {
  title: string;
  icon: string;
  iconBg: string;
  data: string[];
}

@Component({
  selector: 'app-understanding',
  templateUrl: './understanding.component.html',
  styleUrls: ['./understanding.component.scss'],
  standalone: true,
  imports: [CommonModule, CaptionComponent],
})
export class UnderstandingComponent {
  trashIcon: string = '/icons/awe_trash.svg';
  editIcon: string = '/icons/awe_edit.svg';
  errorIcon: string = '/icons/awe_error.svg';
  roboBallIcon: string = '/icons/robo_ball.svg';
  rightArrowIcon: string = '/icons/right_arrow.svg';

  // Card icons
  problemIcon: string = '/cards-icons/problem.svg';
  solutionIcon: string = '/cards-icons/solution.svg';
  keyPartnerIcon: string = '/cards-icons/key-pattern.svg';
  valuePropositionIcon: string = '/cards-icons/value-proposition.svg';
  customerSegmentsIcon: string = '/cards-icons/customer-segments.svg';
  keyMetricsIcon: string = '/cards-icons/key-metrics.svg';
  alternativesIcon: string = '/cards-icons/alternatives.svg';
  costStructureIcon: string = '/cards-icons/cost-structure.svg';
  revenueStreamsIcon: string = '/cards-icons/revenue-streams.svg';

  businessModelCanvas: CanvasItem[] = [
    {
      title: 'Problem',
      icon: this.problemIcon,
       iconBg: '#ffebee',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      title: 'Key partners',
      icon: this.keyPartnerIcon,
       iconBg: '#e3f2fd',
      data: Array(6).fill('Dummy data for wireframe design'),
    },
    {
      title: 'Value Proposition',
      icon: this.valuePropositionIcon,
       iconBg: '#fff3e0',
      data: Array(6).fill('Dummy data for wireframe design'),
    },
    {
      title: 'Customer Segments',
      icon: this.customerSegmentsIcon,
        iconBg: '#f3e5f5',
      data: Array(6).fill('Dummy data for wireframe design'),
    },
    {
      title: 'Key Metrics',
      icon: this.keyMetricsIcon,
      iconBg: '#e8eaf6',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      title: 'Solution',
      icon: this.solutionIcon,
      iconBg: '#e8f5e9',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      title: 'Alternatives',
      icon: this.alternativesIcon,
        iconBg: '#ede7f6',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      title: 'Cost Structure',
      icon: this.costStructureIcon,
       iconBg: '#fce4ec',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
    {
      title: 'Revenue Streams',
      icon: this.revenueStreamsIcon,
        iconBg: '#e0f2f1',
      data: [
        'Dummy data for wireframe design',
        'Dummy data for wireframe design',
      ],
    },
  ];

  // Organize items by their position in the canvas
  get firstRowFirstColumn(): CanvasItem[] {
    return [this.businessModelCanvas[0], this.businessModelCanvas[4]]; // Problem, Key Metrics
  }

  get firstRowSecondColumn(): CanvasItem[] {
    return [
      this.businessModelCanvas[1],
      this.businessModelCanvas[2],
      this.businessModelCanvas[3],
    ]; // Key partners, Value Proposition, Customer Segments
  }

  get firstRowThirdColumn(): CanvasItem[] {
    return [this.businessModelCanvas[5], this.businessModelCanvas[6]]; // Solution, Alternatives
  }

  get secondRowItems(): CanvasItem[] {
    return [this.businessModelCanvas[7], this.businessModelCanvas[8]]; // Cost Structure, Revenue Streams
  }

  onEdit(item: CanvasItem): void {
    console.log('Edit:', item.title);
    // Implement edit functionality
  }

  onDelete(item: CanvasItem): void {
    console.log('Delete:', item.title);
    // Implement delete functionality
  }
}
