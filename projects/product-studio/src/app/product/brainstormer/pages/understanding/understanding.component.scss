// Main row container with equal height columns
.canvas-row {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;

  // Ensure all columns have equal height
  > [class*="col-"] {
    display: flex;
    flex-direction: column;
  }
}

.canvas-card {
  padding: 24px;
  border-radius: 1rem;
  border: 1px solid #dedddd;
  background: var(--Neutral-N--50, #fff);
  box-shadow: 0px 0px 16px 0px rgba(225, 225, 225, 0.25);
  backdrop-filter: blur(25px);
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }

  // Ensure card body takes remaining space
  .card-body {
    flex-grow: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
}
.card-icon {
  width: 40px;
  border-radius: 50%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  .card-icon-img {
    width: 24px;
    height: 24px;
    object-fit: contain;
    margin: 0;
  }
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  // gap: 8px;

  button{
    min-width: 0
  }

  .icon-button {
    background: none;
    border: none;
    cursor: pointer;
    width: 32px;
    height: 32px;

    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

.custom-card-header {
  // background-color: #f8f9fa;
  // border-bottom: 1px solid #dee2e6;

  h6 {
    color: #292C3D;
    font-size: 0.875rem;
  }
}

.card-body {
  font-size: 0.8rem;
  padding: 1rem 0.5rem 0.5rem 1rem !important;

  ul li {
    line-height: 1.4;
    word-wrap: break-word;
  }
}

// Column containers for equal heights
.column-container {
  display: flex;
  height: 100%;

  &.first-column,
  &.third-column {
    flex-direction: column;
    gap: 0.5rem;

    .canvas-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
  }

  &.second-column {
    flex-direction: row;
    gap: 0.5rem;

    .canvas-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
      min-height: 0;
    }
  }
}

// Media queries for responsive design
@media (min-width: 992px) {
  // Large screens (lg) - maintain row layout for second column
  .canvas-row {
    display: flex;
    align-items: stretch;
  }

  .column-container.second-column {
    flex-direction: row;

    .canvas-card {
      min-width: calc(33.333% - 0.33rem);
    }
  }
}

@media (max-width: 991.98px) and (min-width: 768px) {
  // Medium screens (md) - stack second column cards vertically
  .canvas-row {
    min-height: auto;
  }

  .column-container.second-column {
    flex-direction: column;

    .canvas-card {
      min-height: 150px;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .custom-card-header h6 {
    font-size: 0.8rem;
  }

  .card-body {
    font-size: 0.75rem;
  }
}

@media (max-width: 767.98px) {
  // Small screens (sm) - all cards stack vertically
  .canvas-row {
    min-height: auto;
  }

  .column-container {
    &.first-column,
    &.second-column,
    &.third-column {
      flex-direction: column;

      .canvas-card {
        min-height: 120px;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .custom-card-header {
    padding: 0.5rem !important;

    h6 {
      font-size: 0.75rem;
    }
  }

  .card-body {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.7rem;
  }
}

@media (max-width: 576px) {
  // Extra small screens (xs)
  .column-container .canvas-card {
    min-height: 100px;
  }

  .custom-card-header h6 {
    font-size: 0.7rem;
  }

  .card-body {
    font-size: 0.65rem;
    padding: 0.4rem 0.6rem !important;
  }

  .card-icon {
    width: 32px;
    height: 32px;

    .card-icon-img {
      width: 20px;
      height: 20px;
    }
  }
}
