<div class="container-fluid p-3">
  <!-- First Row -->
  <div class="row canvas-row">
    <!-- First Column - 2 cards stacked -->
    <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
      <div class="column-container first-column">
        <div *ngFor="let item of firstRowFirstColumn" class="canvas-card">
          <div
            class="custom-card-header me-3 d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center">
              <div
                class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg"
              >
                <img
                  class="card-icon-img fs-5"
                  src="{{ item.icon }}"
                  alt="{{ item.title }}"
                />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">
              <button class="icon-button">
                <img [src]="errorIcon" alt="Error" />
              </button>
              <button class="icon-button">
                <img [src]="editIcon" alt="Edit" />
              </button>
              <button class="icon-button">
                <img [src]="trashIcon" alt="Delete" />
              </button>
            </div>
          </div>
          <div class="card-body p-3">
            <ul class="list-unstyled mb-0">
              <li
                *ngFor="let dataItem of item.data; let i = index"
                class="mb-2 d-flex align-items-start"
              >
                <span class="me-2 text-muted">{{ i + 1 }}.</span>
                <awe-caption variant="s2" type="regular" id="subtitle">
                  {{ dataItem }}
                </awe-caption>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Second Column - 3 cards in row -->
    <div class="col-lg-6 col-md-12 col-sm-12 mb-3">
      <div class="column-container second-column">
        <div *ngFor="let item of firstRowSecondColumn" class="canvas-card">
          <div
            class="custom-card-header m-3 d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center">
              <div
                class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg"
              >
                <img
                  class="card-icon-img fs-5"
                  src="{{ item.icon }}"
                  alt="{{ item.title }}"
                />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">
              <button class="icon-button">
                <img [src]="errorIcon" alt="Error" loading="eager" />
              </button>
              <button class="icon-button">
                <img [src]="editIcon" alt="Edit" loading="eager" />
              </button>
              <button class="icon-button">
                <img [src]="trashIcon" alt="Delete" loading="eager" />
              </button>
            </div>
          </div>
          <div class="card-body p-3">
            <ul class="list-unstyled mb-0">
              <li
                *ngFor="let dataItem of item.data; let i = index"
                class="mb-2 d-flex align-items-start"
              >
                <span class="me-2 text-muted">{{ i + 1 }}.</span>
                <awe-caption variant="s2" type="regular" id="subtitle">
                  {{ dataItem }}
                </awe-caption>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Third Column - 2 cards stacked -->
    <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
      <div class="column-container third-column">
        <div *ngFor="let item of firstRowThirdColumn" class="canvas-card">
          <div
            class="custom-card-header me-3 d-flex justify-content-between align-items-center"
          >
            <div class="d-flex align-items-center">
              <div
                class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
                [style.background-color]="item.iconBg"
              >
                <img
                  class="card-icon-img fs-5"
                  src="{{ item.icon }}"
                  alt="{{ item.title }}"
                />
              </div>
              <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
            </div>
            <div class="card-actions">
              <button class="icon-button">
                <img [src]="errorIcon" alt="Error" loading="eager" />
              </button>
              <button class="icon-button">
                <img [src]="editIcon" alt="Edit" loading="eager" />
              </button>
              <button class="icon-button">
                <img [src]="trashIcon" alt="Delete" loading="eager" />
              </button>
            </div>
          </div>
          <div class="card-body p-3">
            <ul class="list-unstyled mb-0">
              <li
                *ngFor="let dataItem of item.data; let i = index"
                class="mb-2 d-flex align-items-start"
              >
                <span class="me-2 text-muted">{{ i + 1 }}.</span>
                <awe-caption variant="s2" type="regular" id="subtitle">
                  {{ dataItem }}
                </awe-caption>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Second Row - 2 cards side by side -->
  <div class="row canvas-row">
    <div
      class="col-lg-6 col-md-6 col-sm-12 mb-3"
      *ngFor="let item of secondRowItems"
    >
      <div class="canvas-card">
        <div
          class="custom-card-header d-flex justify-content-between align-items-center"
        >
          <div class="d-flex align-items-center">
            <div
              class="card-icon rounded-circle d-flex align-items-center justify-content-center flex-shrink-0"
              [style.background-color]="item.iconBg"
            >
              <img
                class="card-icon-img fs-5"
                src="{{ item.icon }}"
                alt="{{ item.title }}"
              />
            </div>
            <h6 class="mb-0 fw-bold m-2">{{ item.title }}</h6>
          </div>
          <div class="card-actions">
            <button class="icon-button">
              <img [src]="errorIcon" alt="Error" loading="eager" />
            </button>
            <button class="icon-button">
              <img [src]="editIcon" alt="Edit" loading="eager" />
            </button>
            <button class="icon-button">
              <img [src]="trashIcon" alt="Delete" loading="eager" />
            </button>
          </div>
        </div>
        <div class="card-body p-3">
          <ul class="list-unstyled mb-0">
            <li
              *ngFor="let dataItem of item.data; let i = index"
              class="mb-2 d-flex align-items-start"
            >
              <span class="me-2 text-muted">{{ i + 1 }}.</span>
              <awe-caption variant="s2" type="regular" id="subtitle">
                {{ dataItem }}
              </awe-caption>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
